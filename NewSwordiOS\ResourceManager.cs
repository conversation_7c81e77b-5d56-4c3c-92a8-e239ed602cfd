using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using Foundation;

namespace NewSwordiOS
{
    public static class ResourceManager
    {
        // 是否强制覆盖已存在的文件
        public static bool ForceOverride { get; set; } = false;

        /// <summary>
        /// 检查并更新资源文件。
        /// </summary>
        /// <param name="directoriesToCopy">需要更新的资源目录</param>
        /// <param name="internalStoragePath">内部存储路径</param>
        public static void CheckAndUpdateResources(string[] directoriesToCopy, string internalStoragePath)
        {
            Console.WriteLine($"Checking resources in: {internalStoragePath}");
            const string versionFileName = "version.json";
            string internalVersionFilePath = Path.Combine(internalStoragePath, versionFileName);

            // 读取应用包中的版本号
            string assetsVersion = ReadVersionFromBundle(versionFileName);

            // 读取内部存储中的版本号
            string internalVersion = File.Exists(internalVersionFilePath)
                ? File.ReadAllText(internalVersionFilePath)
                : null;

            Console.WriteLine($"Internal version file path: {internalVersionFilePath}");

            // 如果版本号不匹配或者内部存储中没有版本文件，执行更新
            // if (true)
            if (string.IsNullOrEmpty(internalVersion) || string.CompareOrdinal(assetsVersion, internalVersion) > 0)
            {
                Console.WriteLine($"Updating resources: Bundle version {assetsVersion}, Internal version {internalVersion ?? "null"}");

                // 复制资源目录
                foreach (var dir in directoriesToCopy)
                {
                    string sourceDir = dir;
                    string targetDir = Path.Combine(internalStoragePath, dir);

                    // 确保创建必要的子目录结构
                    if (dir == "save")
                    {
                        // 确保 save/game 目录存在
                        string gameDir = Path.Combine(targetDir, "game");
                        Directory.CreateDirectory(gameDir);
                        Console.WriteLine($"Created directory: {gameDir}");

                        // 确保 save/rpg 目录存在
                        string rpgDir = Path.Combine(targetDir, "rpg");
                        Directory.CreateDirectory(rpgDir);
                        Console.WriteLine($"Created directory: {rpgDir}");

                        // 确保 save/Shot 目录存在
                        string shotDir = Path.Combine(targetDir, "Shot");
                        Directory.CreateDirectory(shotDir);
                        Console.WriteLine($"Created directory: {shotDir}");
                    }

                    CopyDirectoryFromBundle(sourceDir, targetDir);
                }

                // 更新内部存储的版本文件
                File.WriteAllText(internalVersionFilePath, assetsVersion);
                Console.WriteLine("Resource update complete.");
            }
            else
            {
                Console.WriteLine($"No update needed: Bundle version {assetsVersion}, Internal version {internalVersion}");
            }
        }

        /// <summary>
        /// 从应用包中读取版本信息
        /// </summary>
        private static string ReadVersionFromBundle(string versionFileName)
        {
            // 获取文件名和扩展名
            string fileName = Path.GetFileNameWithoutExtension(versionFileName);
            string extension = Path.GetExtension(versionFileName);
            if (!string.IsNullOrEmpty(extension) && extension.StartsWith('.'))
            {
                extension = extension[1..]; // 移除扩展名前的点号
            }

            // 首先尝试在根目录查找版本文件
            string versionPath = NSBundle.MainBundle.PathForResource(fileName, extension);

            //// 如果找不到，尝试在应用包中搜索
            //if (string.IsNullOrEmpty(versionPath))
            //{
            //    Console.WriteLine($"Version file {versionFileName} not found in bundle root, searching in all directories");

            //    string bundlePath = NSBundle.MainBundle.BundlePath;
            //    var matchingFiles = Directory.GetFiles(bundlePath, $"{fileName}.{extension}", SearchOption.AllDirectories);

            //    if (matchingFiles.Length > 0)
            //    {
            //        versionPath = matchingFiles[0];
            //        Console.WriteLine($"Found version file at: {versionPath}");
            //    }
            //}

            if (string.IsNullOrEmpty(versionPath))
            {
                Console.WriteLine($"Version file {versionFileName} not found in bundle, using default version");
                // 如果找不到版本文件，返回默认版本
                return "0.0.1";
            }

            try
            {
                string version = File.ReadAllText(versionPath);
                Console.WriteLine($"Read version from bundle: {version}");
                return version;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading version file: {ex.Message}");
                return "0.0.1";
            }
        }

        /// <summary>
        /// 递归复制应用包中的目录及其内容到内部存储。
        /// </summary>
        /// <param name="sourceDir">应用包中的目录路径</param>
        /// <param name="targetDir">内部存储中的目标目录路径</param>
        public static void CopyDirectoryFromBundle(string sourceDir, string targetDir)
        {
            // 确保目标目录存在
            Directory.CreateDirectory(targetDir);

            // 获取应用包中的资源路径
            NSBundle mainBundle = NSBundle.MainBundle;
            string bundlePath = mainBundle.BundlePath;
            string resourcesPath = Path.Combine(bundlePath, sourceDir);

            Console.WriteLine($"Looking for resources in: {resourcesPath}");

            // 检查资源路径是否存在
            if (Directory.Exists(resourcesPath))
            {
                // 复制目录中的所有文件
                foreach (string filePath in Directory.GetFiles(resourcesPath))
                {
                    string fileName = Path.GetFileName(filePath);
                    string targetFilePath = Path.Combine(targetDir, fileName);

                    // 如果目标文件已存在且不强制覆盖，则跳过
                    if (File.Exists(targetFilePath) && !ForceOverride)
                    {
                        Console.WriteLine($"File already exists: {targetFilePath}");
                        continue;
                    }

                    File.Copy(filePath, targetFilePath, true);
                    Console.WriteLine($"Copied file: {filePath} -> {targetFilePath}");
                }

                // 递归复制子目录
                foreach (string dirPath in Directory.GetDirectories(resourcesPath))
                {
                    string dirName = Path.GetFileName(dirPath);
                    string targetSubDir = Path.Combine(targetDir, dirName);
                    CopyDirectoryFromBundle(Path.Combine(sourceDir, dirName), targetSubDir);
                }
            }
            else
            {
                Console.WriteLine($"Source directory not found in bundle: {sourceDir}");

                // 即使源目录不存在，也确保创建目标目录
                // 这样可以确保目录结构完整，即使没有文件可复制
                Console.WriteLine($"Creating empty directory: {targetDir}");
                Directory.CreateDirectory(targetDir);
            }
        }
    }
}
