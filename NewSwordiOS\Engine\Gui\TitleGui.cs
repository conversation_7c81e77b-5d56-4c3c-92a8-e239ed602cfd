﻿using System;
using System.IO;
using Engine.Gui.Base;
using Engine.Script;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input;
using Texture = Engine.Gui.Base.Texture;

namespace Engine.Gui
{
    public sealed class TitleGui : GuiItem
    {
        public int CurrentIndex { get; set; } = 0;

        private readonly GuiItem _initButton;
        private readonly GuiItem _loadButton;
        private readonly GuiItem _teamButton;
        private readonly GuiItem _exitButton;
        private readonly GuiItem[] _guiItems;
        public TitleGui()
        {
            var bgImage = Globals.GetRightpath(GuiManager.Setttings.Sections["Title"]["BackgroundImage"]);
            using (var fs = FileManager.OpenRead(bgImage))
            {
                BaseTexture = new Texture(new Asf(
                Texture2D.FromStream(Globals.TheGame.GraphicsDevice, fs)));
            }

            Width = BaseTexture.Width;
            Height = BaseTexture.Height;
            int topAdjust = 0;
            int leftAdjust = 0;
            try
            {
                if (GuiManager.Setttings.Sections["Title"].ContainsKey("TopAdjust"))
                {
                    topAdjust = int.Parse(GuiManager.Setttings.Sections["Title"]["TopAdjust"]);
                }
                if (GuiManager.Setttings.Sections["Title"].ContainsKey("LeftAdjust"))
                {
                    leftAdjust = int.Parse(GuiManager.Setttings.Sections["Title"]["LeftAdjust"]);
                }
            }
            catch (System.Exception)
            {
            }
            Position = new Vector2(
                (Globals.WindowWidth - Width) / 2f + leftAdjust,
                (Globals.WindowHeight - Height) / 2f + topAdjust);
            var cfg = GuiManager.Setttings.Sections["Title_Btn_Begin"];
            var asf = Utils.GetAsf(null, cfg["Image"]);
            var sound = Utils.GetSoundEffect(cfg["Sound"]);
            _initButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                new Texture(asf, 0, 1),
                new Texture(asf, 1, 1),
                null,
                sound);
            cfg = GuiManager.Setttings.Sections["Title_Btn_Load"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _loadButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                new Texture(asf, 0, 1),
                new Texture(asf, 1, 1),
                null,
                sound);
            cfg = GuiManager.Setttings.Sections["Title_Btn_Team"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _teamButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                new Texture(asf, 0, 1),
                new Texture(asf, 1, 1),
                null,
                sound);
            cfg = GuiManager.Setttings.Sections["Title_Btn_Exit"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _exitButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                new Texture(asf, 0, 1),
                new Texture(asf, 1, 1),
                null,
                sound);
            _guiItems = new[]
            {
                _initButton,
                _loadButton,
                _teamButton,
                _exitButton
            };
            RegisterEvent();

            IsShow = false;
        }

        private void RegisterEvent()
        {
            _initButton.Click += InitButtonClicked;
            _loadButton.Click += LoadButtonClicked;
            _teamButton.Click += TeamButtonClicked;
            _exitButton.Click += ExitButtonClicked;
        }

        private void InitButtonClicked(object arg1, MouseLeftClickEvent arg2)
        {
            IsShow = false;
            ScriptManager.RunScript(Utils.GetScriptParser("NewGame.txt"));
        }

        private void LoadButtonClicked(object arg1, MouseLeftClickEvent arg2)
        {
            IsShow = false;
            GuiManager.ShowLoad();
        }

        private void TeamButtonClicked(object arg1, MouseLeftClickEvent arg2)
        {
            IsShow = false;
            ScriptManager.RunScript(Utils.GetScriptParser("team.txt"));
        }

        private void ExitButtonClicked(object arg1, EventArgs arg2)
        {
            Globals.TheGame.ExitGame();
        }

        public override void Update(GameTime gameTime)
        {
            if(!IsShow) return;
            base.Update(gameTime);
            HandleInput();
            foreach (var guiItem in _guiItems)
            {
                guiItem.Update(gameTime);
            }
        }

        private void HandleInput()
        {
            var keyboardState = Keyboard.GetState();
            var gamepadState = GamePad.GetState(PlayerIndex.One);
            if ((keyboardState.IsKeyDown(Keys.Down) && Globals.TheGame.LastKeyboardState.IsKeyUp(Keys.Down))
                || (gamepadState.IsButtonDown(Buttons.DPadDown) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.DPadDown)))
            {
                CurrentIndex = (CurrentIndex + 1) % _guiItems.Length;
                //_guiItems[CurrentIndex].IsShow = false;
                HanldeButtonDisplay();
            }
            else if ((keyboardState.IsKeyDown(Keys.Up) && Globals.TheGame.LastKeyboardState.IsKeyUp(Keys.Up))
                || (gamepadState.IsButtonDown(Buttons.DPadUp) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.DPadUp)))
            {
                CurrentIndex = (CurrentIndex - 1 + _guiItems.Length) % _guiItems.Length;
                //_guiItems[CurrentIndex].IsShow = false
                HanldeButtonDisplay();
            }
            if ((keyboardState.IsKeyDown(Keys.Enter) && Globals.TheGame.LastKeyboardState.IsKeyUp(Keys.Enter))
                || (gamepadState.IsButtonDown(Buttons.A) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.A)))
            {
                if(!_guiItems[CurrentIndex].IsShow)
                {
                    //触发点击事件
                    _guiItems[CurrentIndex].InvokeClick(null, null);
                }
            }
        }

        private void HanldeButtonDisplay()
        {
            for (var i = 0; i < _guiItems.Length; i++)
            {
                if (i == CurrentIndex)
                {
                    _guiItems[i].IsShow = false;
                }
                else
                {
                    _guiItems[i].IsShow = true;
                }
            }
        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;
            base.Draw(spriteBatch);
            foreach (var guiItem in _guiItems)
            {
                guiItem.Draw(spriteBatch);
            }
        }
    }
}
