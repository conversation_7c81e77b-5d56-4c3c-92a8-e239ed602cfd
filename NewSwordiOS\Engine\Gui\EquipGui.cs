﻿using Engine.Gui.Base;
using Engine.ListManager;
using Engine.Storage;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using System.IO;
using Texture = Engine.Gui.Base.Texture;
using System;
using System.Linq;

namespace Engine.Gui
{
    public sealed class EquipGui : GuiItem
    {
        private int _index = 0; //当前显示角色
        private DragDropItem[] _equipitems = new DragDropItem[7];
        private DragDropItem _head;
        private DragDropItem _neck;
        private DragDropItem _body;
        private DragDropItem _back;
        private DragDropItem _hand;
        private DragDropItem _wrist;
        private DragDropItem _foot;
        private GuiTabItem[] _personNameItem = new GuiTabItem[4]; //左侧名字
        private bool _isShow;
        private TextGui[] _statusitems;  //状态项

        public override bool IsShow
        {
            set
            {
                _isShow = value;
                /*
                if (_index != Globals.PlayerIndex)
                {
                    _personNameItem[_index].IsSelected = false;
                    _personNameItem[Globals.PlayerIndex].IsSelected = true;
                    _index = Globals.PlayerIndex; 
                }*/
                if (value)
                {
                    RefreshNameItemStatus();
                }
            }
            get { return _isShow; }
        }

        private void RefreshNameItemStatus()
        {
            for (int i = 0; i < _personNameItem.Length; i++)
            {
                _personNameItem[i].IsEnable = NpcManager.PlayerInTeam(i);
                _personNameItem[i].IsSelected = i == Index;
            }
        }

        public int Index
        {
            get
            {
                return _index;
            }
            set
            {
                _index = value;
                RefreshNameItemStatus();
            }
        }

        //新剑侠
        public EquipGui()
        {
            //武功项
            var cfg = GuiManager.Setttings.Sections["Magics"];
            var baseTexture = new Texture(Utils.GetAsf(null, cfg["Image"]));
            Width = baseTexture.Width;
            Height = baseTexture.Height;
            var position = new Vector2(
                Globals.WindowWidth / 2f - Width + int.Parse(cfg["LeftAdjust"]),
                0f + int.Parse(cfg["TopAdjust"]));
            var scrollbar = new Vector2(
                Globals.WindowWidth / 2f - Width + int.Parse(cfg["ScrollBarLeft"]),
                0f + int.Parse(cfg["ScrollBarRight"]));

            //装备
            cfg = GuiManager.Setttings.Sections["Equip"];
            BaseTexture = new Texture(Utils.GetAsf(null, cfg["Image"]));
            Width = BaseTexture.Width;
            Height = BaseTexture.Height;
            Position = new Vector2(
                Globals.WindowWidth / 2f - Width + int.Parse(cfg["LeftAdjust"]),
                0f + int.Parse(cfg["TopAdjust"]));

            cfg = GuiManager.Setttings.Sections["Equip_Head"];
            var hasCount = GoodsListManager.Type != GoodsListManager.ListType.TypeByGoodItem;
            _equipitems[0] = _head = new DragDropItem(this,//Head
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                null,
                new GoodsGui.GoodItemData(GoodsListManager.EquipIndexBegin),
                hasCount);

            cfg = GuiManager.Setttings.Sections["Equip_Neck"];
            _equipitems[1] = _neck = new DragDropItem(this, //Neck
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                null,
                new GoodsGui.GoodItemData(GoodsListManager.EquipIndexBegin+1),
                hasCount);

            cfg = GuiManager.Setttings.Sections["Equip_Body"];
            _equipitems[2] = _body = new DragDropItem(this, //Body
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                null,
                new GoodsGui.GoodItemData(GoodsListManager.EquipIndexBegin+2),
                hasCount);

            cfg = GuiManager.Setttings.Sections["Equip_Back"];
            _equipitems[3] = _back = new DragDropItem(this, //Back
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                null,
                new GoodsGui.GoodItemData(GoodsListManager.EquipIndexBegin+3),
                hasCount);

            cfg = GuiManager.Setttings.Sections["Equip_Hand"];
            _equipitems[4] = _hand = new DragDropItem(this, //Hand
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                null,
                new GoodsGui.GoodItemData(GoodsListManager.EquipIndexBegin+4),
                hasCount);

            cfg = GuiManager.Setttings.Sections["Equip_Wrist"];
            _equipitems[5] = _wrist = new DragDropItem(this, //Wrist
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                null,
                new GoodsGui.GoodItemData(GoodsListManager.EquipIndexBegin+5),
                hasCount);

            cfg = GuiManager.Setttings.Sections["Equip_Foot"];
            _equipitems[6] = _foot = new DragDropItem(this, //Foot
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                null,
                new GoodsGui.GoodItemData(GoodsListManager.EquipIndexBegin+6),
                hasCount);
            #region 状态数值显示,与月影传说不同
            //读状态设置
            _statusitems = new TextGui[9];
            cfg = GuiManager.Setttings.Sections["State_Level"];
            _statusitems[0] = new TextGui(this, //Level
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                Globals.FontSize7,
                0,
                0,
                "",
                Utils.GetColor(cfg["Color"]));
            cfg = GuiManager.Setttings.Sections["State_Exp"];
            _statusitems[1] = new TextGui(this, //Exp
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                Globals.FontSize7,
                0,
                0,
                "",
                Utils.GetColor(cfg["Color"]));
            cfg = GuiManager.Setttings.Sections["State_LevelUp"];
            _statusitems[2] = new TextGui(this, //LevelUp
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                Globals.FontSize7,
                0,
                0,
                "",
                Utils.GetColor(cfg["Color"]));
            cfg = GuiManager.Setttings.Sections["State_Life"];
            _statusitems[3] = new TextGui(this, //Life
               new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                Globals.FontSize7,
                0,
                0,
                "",
                Utils.GetColor(cfg["Color"]));
            cfg = GuiManager.Setttings.Sections["State_Thew"];
            _statusitems[4] = new TextGui(this, //Thew
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                Globals.FontSize7,
                0,
                0,
                "",
                Utils.GetColor(cfg["Color"]));
            cfg = GuiManager.Setttings.Sections["State_Mana"];
            _statusitems[5] = new TextGui(this, //Mana
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                Globals.FontSize7,
                0,
                0,
                "",
                Utils.GetColor(cfg["Color"]));
            cfg = GuiManager.Setttings.Sections["State_Attack"];
            _statusitems[6] = new TextGui(this, //Attack
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                Globals.FontSize7,
                0,
                0,
                "",
                Utils.GetColor(cfg["Color"]));
            cfg = GuiManager.Setttings.Sections["State_Defend"];
            _statusitems[7] = new TextGui(this, //Defend
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                Globals.FontSize7,
                0,
                0,
                "",
                Utils.GetColor(cfg["Color"]));
            cfg = GuiManager.Setttings.Sections["State_Evade"];
            _statusitems[8] = new TextGui(this, // Evade
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                Globals.FontSize7,
                0,
                0,
                "",
                Utils.GetColor(cfg["Color"]));
            #endregion

            //人名
            for (int personIndex = 0; personIndex < 4; personIndex++)
            {
                string key = "Equip_player" + (personIndex + 1);
                //int startFrame = playerInTeam(personIndex) ? 1 : 0;
                cfg = GuiManager.Setttings.Sections[key];
                var asf = Utils.GetAsf(null, cfg["Image"]);
                var sound = Utils.GetSoundEffect(cfg["Sound"]);
                _personNameItem[personIndex] = new GuiTabItem(this,
                    new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                    asf.Width,
                    asf.Height,
                    new Texture(asf, 0, 1),
                    new Texture(asf, 1, 1),
                    null,
                    null,
                    new Texture(asf, 2, 1),//selected
                    null,
                    sound)
                {
                    IsShow = true
                };
            }
            _index = Globals.PlayerIndex;
            _personNameItem[_index].IsSelected = true;
            
            RegisterEventHandler();

            IsShow = false;
        }
        /// <summary>
        /// 切换当前人物详情显示
        /// </summary>
        /// <param name="toPersonIndex"></param>
        /// <param name="arg1"></param>
        private void SwitchPersonInfoTab(int toPersonIndex)
        {
            if (NpcManager.PlayerInTeam(toPersonIndex))
            {
                for (int i = 0; i < _personNameItem.Length; i++)
                {
                    _personNameItem[i].IsSelected = i == toPersonIndex;
                }
                if (_index != toPersonIndex)
                {
                    for (int i = 0; i < _equipitems.Length; i++)
                    {
                        _equipitems[i].Data = new GoodsGui.GoodItemData(GoodsListManager.EquipIndexEnd - 7 * (toPersonIndex + 1) + 1 + i);
                    }
                }
                else
                {
                    for (int i = 0; i < _equipitems.Length; i++)
                    {
                        _equipitems[i].Data = new GoodsGui.GoodItemData(GoodsListManager.EquipIndexBegin + i);
                    }
                }
                _index = toPersonIndex;
            }
            else
            {
                var text = "该角色不可查看!";
                GuiManager.ShowMessage(text);
            }
        }

        /*
        private void LoadPersonInfoByIndex(int toPersonIndex)
        {            
            if (toPersonIndex != _index)
            {
                if (NpcManager.PlayerInTeam(toPersonIndex))
                {
                    _index = toPersonIndex;

                    var path = @"save\game\" + "Goods" + _index + ".ini";
                    if (!File.Exists(path))
                    {
                        path = @"ini\save\" + "Goods" + _index + ".ini";
                    }
                    GoodsListManager.LoadList(path);
                }
            }
        }*/

        private void RegisterEventHandler()
        {
            int index, sourceIndex;
            for (int i = 0; i < _personNameItem.Length; i++)
            {
                int curIndex = i;
                _personNameItem[curIndex].MouseLeftDown += (arg1, arg2) => SwitchPersonInfoTab(curIndex);
                //_personNameItem[curIndex].TabSelected += (arg1, arg2) => LoadPersonInfoByIndex(curIndex);
            }
            _head.Drop += (arg1, arg2) =>
            {
                if (CanDrop(arg2, Good.EquipPosition.Head))
                {
                    if (GoodsGui.ExchangeItem(arg1, arg2, out index, out sourceIndex))
                    {

                    }
                }
            };
            _neck.Drop += (arg1, arg2) =>
            {
                if (CanDrop(arg2, Good.EquipPosition.Neck))
                {
                    if (GoodsGui.ExchangeItem(arg1, arg2, out index, out sourceIndex))
                    {

                    }
                }
            };
            _body.Drop += (arg1, arg2) =>
            {
                if (CanDrop(arg2, Good.EquipPosition.Body))
                {
                    if (GoodsGui.ExchangeItem(arg1, arg2, out index, out sourceIndex))
                    {

                    }
                }
            };
            _back.Drop += (arg1, arg2) =>
            {
                if (CanDrop(arg2, Good.EquipPosition.Back))
                {
                    if (GoodsGui.ExchangeItem(arg1, arg2, out index, out sourceIndex))
                    {

                    }
                }
            };
            _hand.Drop += (arg1, arg2) =>
            {
                if (CanDrop(arg2, Good.EquipPosition.Hand))
                {
                    if (GoodsGui.ExchangeItem(arg1, arg2, out index, out sourceIndex))
                    {

                    }
                }
            };
            _wrist.Drop += (arg1, arg2) =>
            {
                if (CanDrop(arg2, Good.EquipPosition.Wrist))
                {
                    if (GoodsGui.ExchangeItem(arg1, arg2, out index, out sourceIndex))
                    {

                    }
                }
            };
            _foot.Drop += (arg1, arg2) =>
            {
                if (CanDrop(arg2, Good.EquipPosition.Foot))
                {
                    if (GoodsGui.ExchangeItem(arg1, arg2, out index, out sourceIndex))
                    {

                    }
                }
            };

            foreach (var item in _equipitems)
            {
                item.RightClick += (arg1, arg2) =>
                {
                    var theItem = (DragDropItem) arg1;
                    var data = theItem.Data as GoodsGui.GoodItemData;
                    if (data != null)
                    {
                        int newIndex;
                        if (GoodsListManager.PlayerUnEquiping(data.Index, out newIndex))
                        {
                            theItem.BaseTexture = null;
                            theItem.TopLeftText = "";
                            GuiManager.UpdateGoodItemView(newIndex);
                        }
                    }
                };
                item.MouseStayOver += GoodsGui.MouseStayOverHandler;
                item.MouseLeave += GoodsGui.MouseLeaveHandler;
            }
        }

        public void UpdateItems()
        {
            foreach (var item in _equipitems)
            {
                var index = ((GoodsGui.GoodItemData) item.Data).Index;
                if (Index == Globals.PlayerIndex)
                {
                    item.BaseTexture = GoodsListManager.GetTexture(index);
                    var info = GoodsListManager.GetItemInfo(index);
                    if (info != null && info.Count > 0)
                    {
                        item.TopLeftText = info.Count.ToString();
                    }
                    else
                    {
                        item.TopLeftText = "";
                    }
                }
                else
                {
                    Partner partner = NpcManager.GetPartnerByName(PartnerList.GetName(Index));
                    if (partner != null)
                    {
                        //int equiptIndex = index - GoodsListManager.EquipIndexBegin + 1;
                        if (GoodsListManager.IsInPartnerEquipRange(index))
                        {
                            /*
                            if (GoodsListManager.GetItemInfo(index) == null)
                            {
                                Good equiptGood = partner.GetEquiptGood(index - (GoodsListManager.PartnerEquipIndexEnd - 7 * Index));
                                if (equiptGood != null)
                                {
                                    var partnerEquipInfo = new GoodsListManager.GoodsItemInfo(equiptGood, 1);
                                    GoodsListManager.SetItemInfo(index, partnerEquipInfo); 
                                }
                            }*/
                            item.BaseTexture = GoodsListManager.GetTexture(index);
                            var info = GoodsListManager.GetItemInfo(index);
                            if (info != null && info.Count > 0)
                            {
                                item.TopLeftText = info.Count.ToString();
                            }
                            else
                            {
                                item.TopLeftText = "";
                            }
                        }
                    }
                }
            }
            //状态更新
            UpdateStatItems();
            //武功状态
            //UpdateMagicItems();
            //人名
            //UpdateNameItems();
        }
        //状态
        public void UpdateStatItems()
        {
            if (_index == Globals.PlayerIndex)  //要显示的角色为当前控制的角色
            {
                Player player = Globals.ThePlayer;
                if (player == null)
                {
                    foreach (var item in _statusitems)
                    {
                        item.Text = "";
                    }
                }
                else
                {
                    _statusitems[0].Text = player.Level.ToString();
                    _statusitems[1].Text = player.Exp.ToString();
                    _statusitems[2].Text = player.LevelUpExp.ToString();
                    _statusitems[3].Text = player.Life + "/" + player.LifeMax;
                    _statusitems[4].Text = player.Thew + "/" + player.ThewMax;
                    _statusitems[5].Text = player.ManaLimit
                        ? "1/1"
                        : player.Mana + "/" + player.ManaMax;
                    _statusitems[6].Text = player.Attack.ToString();
                    if (player.Attack2 != 0)
                    {
                        _statusitems[6].Text += string.Format("({0})", player.Attack2);
                    }
                    _statusitems[7].Text = player.Defend.ToString();
                    if (player.Defend2 != 0)
                    {
                        _statusitems[7].Text += string.Format("({0})", player.Defend2);
                    }
                    _statusitems[8].Text = player.Evade.ToString();
                }
            }
            else //要显示的角色为同伴
            {
                var partners = NpcManager.GetAllPartner();
                foreach (var player in partners)
                {
                    int index = PartnerList.GetIndex(player.Name);
                    if (index == _index)
                    {
                        _statusitems[0].Text = player.Level.ToString();
                        _statusitems[1].Text = player.Exp.ToString();
                        _statusitems[2].Text = player.LevelUpExp.ToString();
                        _statusitems[3].Text = player.Life + "/" + player.LifeMax;
                        _statusitems[4].Text = player.Thew + "/" + player.ThewMax;
                        _statusitems[5].Text = player.Mana + "/" + player.ManaMax;
                        _statusitems[6].Text = player.Attack.ToString();
                        if (player.Attack2 != 0)
                        {
                            _statusitems[6].Text += string.Format("({0})", player.Attack2);
                        }
                        _statusitems[7].Text = player.Defend.ToString();
                        if (player.Defend2 != 0)
                        {
                            _statusitems[7].Text += string.Format("({0})", player.Defend2);
                        }
                        _statusitems[8].Text = player.Evade.ToString();
                        break;
                    }
                }
            }            
        }
        private bool CanDrop(DragDropItem.DropEvent arg2, Good.EquipPosition position)
        {
            var sourceItem = arg2.Source;
            var sourceData = sourceItem.Data as GoodsGui.GoodItemData;
            if (sourceData != null)
            {
                Character user = null;
                if (_index != Globals.PlayerIndex)
                {
                    user = NpcManager.GetPartnerByName(PartnerList.GetName(_index));
                }
                else
                {
                    user = Globals.ThePlayer;
                }
                return GoodsListManager.CanEquip(sourceData.Index, position, user);
            }
            return false;
        }

        private static GoodsGui.GoodItemData ToGoodItemData(object data)
        {
            return (GoodsGui.GoodItemData) data;
        }

        public bool EquipGood(int goodListIndex)
        {
            if (GoodsListManager.IsInStoreRange(goodListIndex) ||
                GoodsListManager.IsInBottomGoodsRange(goodListIndex))
            {
                var info = GoodsListManager.GetItemInfo(goodListIndex);
                if (info == null) return false;
                var good = info.TheGood;
                if (good != null &&
                    good.Kind == Good.GoodKind.Equipment)
                {
                    DragDropItem item;
                    var index = 0;
                    switch (good.Part)
                    {
                        case Good.EquipPosition.Body:
                            item = _body;
                            index = ToGoodItemData(item.Data).Index;
                            break;
                        case Good.EquipPosition.Foot:
                            item = _foot;
                            index = ToGoodItemData(item.Data).Index;
                            break;
                        case Good.EquipPosition.Head:
                            item = _head;
                            index = ToGoodItemData(item.Data).Index;
                            break;
                        case Good.EquipPosition.Neck:
                            item = _neck;
                            index = ToGoodItemData(item.Data).Index;
                            break;
                        case Good.EquipPosition.Back:
                            item = _back;
                            index = ToGoodItemData(item.Data).Index;
                            break;
                        case Good.EquipPosition.Wrist:
                            item = _wrist;
                            index = ToGoodItemData(item.Data).Index;
                            break;
                        case Good.EquipPosition.Hand:
                            item = _hand;
                            index = ToGoodItemData(item.Data).Index;
                            break;
                        default:
                            return false;
                    }
                    GoodsListManager.ExchangeListItemAndEquiping(goodListIndex, index);
                    item.BaseTexture = new Texture(good.Image);
                    item.TopLeftText = info.Count.ToString();
                    GuiManager.UpdateGoodItemView(goodListIndex);
                    return true;
                }
            }
            return false;
        }

        public override void Update(GameTime gameTime)
        {
            if (!IsShow) return;
            base.Update(gameTime);
			//修改begin
            UpdateItems();
            foreach (var item in _equipitems)
            {
                item.Update(gameTime);
            }
            //状态
            foreach (var item in _statusitems)
            {
                item.Update(gameTime);
            }
            //人名
            foreach (var item in _personNameItem)
            {
                item.Update(gameTime);
            }
        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;
            base.Draw(spriteBatch);
            //装备
            foreach (var item in _equipitems)
            {
                item.Draw(spriteBatch);
            }
            //状态
            foreach (var item in _statusitems)
            {
                item.Draw(spriteBatch);
            }
            //人名
            foreach (var item in _personNameItem)
            {
                item.Draw(spriteBatch);
            }
        }
    }
}