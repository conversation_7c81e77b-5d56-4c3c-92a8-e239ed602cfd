﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebuggerFlavor>ProjectDebugger</DebuggerFlavor>
  </PropertyGroup>
  <PropertyGroup>
    <ActiveDebugProfile>iPhone</ActiveDebugProfile>
    <SelectedPlatformGroup>RemoteDevice</SelectedPlatformGroup>
    <SelectedDevice>iPhone</SelectedDevice>
    <TargetiOSDevice>&lt;?xml version="1.0" encoding="UTF-8"?&gt;
&lt;!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd"&gt;
&lt;plist version="1.0"&gt;
&lt;dict&gt;
	&lt;key&gt;device&lt;/key&gt;
	&lt;dict&gt;
		&lt;key&gt;architecture&lt;/key&gt;
		&lt;string&gt;ARMv6, ARMv7, ARMv7s, ARM64&lt;/string&gt;
		&lt;key&gt;model&lt;/key&gt;
		&lt;string&gt;iPhone12,1&lt;/string&gt;
		&lt;key&gt;os-version&lt;/key&gt;
		&lt;string&gt;18.3.1&lt;/string&gt;
		&lt;key&gt;os&lt;/key&gt;
		&lt;string&gt;iOS&lt;/string&gt;
	&lt;/dict&gt;
&lt;/dict&gt;
&lt;/plist&gt;
</TargetiOSDevice>
  </PropertyGroup>
  <PropertyGroup Condition="'$(TargetPlatformIdentifier)'=='iOS'">
    <RuntimeIdentifier>ios-arm64</RuntimeIdentifier>
    <PlatformTarget>arm64</PlatformTarget>
  </PropertyGroup>
</Project>