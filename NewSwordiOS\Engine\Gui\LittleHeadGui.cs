﻿using System.Collections.Generic;
using Engine.Gui.Base;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;

namespace Engine.Gui
{
    public sealed class LittleHeadGui : GuiItem
    {
        private Dictionary<string, Asf> _headIco = new Dictionary<string, Asf>();
        private readonly static int LifeBarHeight = 8;
        public static Color LifeColor = new Color(200, 18, 21) * 0.9f;
        public static Color LifeLoseColor = Color.Black * 0.7f;

        public LittleHeadGui()
        {
            IsShow = true;
        }

        public override void Update(GameTime gameTime)
        {
            //do nothing
        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            var partners = NpcManager.GetAllPartner();
            const int x = 5;
            var y = 5;
            foreach (var partner in partners)
            {
                var name = partner.Name;
                if(string.IsNullOrEmpty(name))continue;
                if (!_headIco.ContainsKey(name))
                {
                    _headIco[name] = Utils.GetAsf(@"asf\ui\littlehead\", name + ".asf");
                }
                if(_headIco[name] == null)continue;
                var texture = _headIco[name].GetFrame(0);
                if(texture == null) continue;

                spriteBatch.Draw(texture, new Vector2(x, y), Color.White);

                //绘制队友生命条
                int width = texture.Width;
                float percent = partner.Life / (float)partner.LifeMax;
                int topLeftX = x;
                int topLeftY = y + texture.Height + 2;
                int height = LifeBarHeight;
                var lifeLength = (int)(width * percent);
                var lifeRegion = new Rectangle(topLeftX, topLeftY, lifeLength, height);
                var lifeLoseRegion = new Rectangle(topLeftX + lifeLength, topLeftY, width - lifeLength, height);
                InfoDrawer.DrawRectangle(spriteBatch, lifeRegion, LifeColor);
                InfoDrawer.DrawRectangle(spriteBatch, lifeLoseRegion, LifeLoseColor);


                y += texture.Width + 2;
            }
        }
    }
}