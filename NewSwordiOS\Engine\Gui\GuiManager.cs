﻿using System;
using System.Collections.Generic;
using Engine.Gui.Base;
using Engine.ListManager;
using Engine.Script;
using IniParser;
using IniParser.Model;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Audio;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input;
using Microsoft.Xna.Framework.Input.Touch;
using Texture = Engine.Gui.Base.Texture;

namespace Engine.Gui
{
    public static class GuiManager
    {
        private static SoundEffect _dropSound;
        private static SoundEffect _interfaceShow;
        private static SoundEffect _interfaceMiss;
        private static LinkedList<GuiItem> _allGuiItems = new LinkedList<GuiItem>();
        private static LinkedList<GuiItem> _panels = new LinkedList<GuiItem>();
        private static MouseState _lastMouseState;
        private static KeyboardState _lastKeyboardState;

        private static IList<IGamepadActive> _canActivedItems = new List<IGamepadActive>();

        public static IniData Setttings;
        public static TitleGui TitleInterface;
        public static SaveLoadGui SaveLoadInterface;
        public static SystemGui SystemInterface;
        public static MagicGui MagicInterface;
 //       public static XiuLianGui XiuLianInterface;
        public static GoodsGui GoodsInterface;
        public static MemoGui MemoInterface;
        public static StateGui StateInterface;
        public static EquipGui EquipInterface;
        public static BottomGui BottomInterface;
        public static ColumnGui ColumnInterface;
        public static TopGui TopInterface;
        public static BuyGui BuyInterface;
        public static TimerGui TimerInterface;
        public static LittleMapGui LittleMapInterface;
        public static GambleGui gambleGui;

        //public static WorldMapGui WorldMapInterface;

        public static ToolTipGuiBase ToolTipInterface;
        public static MessageGui MessageInterface;
        public static DialogGui DialogInterface;
        public static LittleHeadGui LittleHeadInterface;

        public static SelectionGui SelectionInterface;

        public static MouseGui MouseInterface;

        public static bool IsMouseStateEated;
        public static DragDropItem DragDropSourceItem;
        public static Texture DragDropSourceTexture;
        public static bool IsDropped;
        public static bool IsShow;

        public static VirtualGamePad TouchPad;
        public static OptionGui OptionInterface;

        // 全局变量，保存当前帧的触摸状态
        public static List<TouchLocation> CurrentTouchState = new List<TouchLocation>();

        // 在主游戏循环中，更新一次触摸状态
        public static void UpdateTouchState()
        {
            CurrentTouchState = new List<TouchLocation>(TouchPanel.GetState());
        }
        public static bool IsTouchConsumed { get; set; }

        private static bool IsTalkNext()
        {
            var mouseState = Mouse.GetState();
            var keyboardState = Keyboard.GetState();
            // 获取当前触摸状态
            //var touchState = TouchPanel.GetState();
            var gamepadState = GamePad.GetState(PlayerIndex.One);

            return (mouseState.LeftButton == ButtonState.Pressed &&
                    _lastMouseState.LeftButton == ButtonState.Released) ||
                   (keyboardState.IsKeyDown(Keys.Space) && _lastKeyboardState.IsKeyUp(Keys.Space)) ||
                   (CurrentTouchState.Count > 0 && CurrentTouchState[0].State == TouchLocationState.Released) ||  // 触摸点击判断
                   IsGamepadPressed(gamepadState, Buttons.A) ||
                   (Globals.TheGame.IsInEditMode && keyboardState.IsKeyDown(Keys.Escape));
        }

        public static void Starting()
        {
            string uipath = @"ini/ui_settings.ini";
            if (Utils.CheckYear() == true)
            {
                uipath = @"ini/ui_settings2.ini";
            }   
            var parser = new FileIniDataParser();
            parser.Parser.Configuration.SkipInvalidLines = true;
            parser.Parser.Configuration.AllowDuplicateKeys = true;
            parser.Parser.Configuration.AllowDuplicateSections = true;
            Setttings = parser.ReadFile(Globals.GetRightpath(uipath), Globals.LocalEncoding);

            GoodsListManager.InitIndex(Setttings);
            MagicListManager.InitIndex(Setttings);

            IsShow = true;

            _dropSound = Utils.GetSoundEffect("界-拖放.wav");
            _interfaceShow = Utils.GetSoundEffect("界-弹出菜单.wav");
            _interfaceMiss = Utils.GetSoundEffect("界-缩回菜单.wav");

            TitleInterface = new TitleGui();
            _allGuiItems.AddLast(TitleInterface);

            SaveLoadInterface = new SaveLoadGui();
            _allGuiItems.AddLast(SaveLoadInterface);

            SystemInterface = new SystemGui();
            _allGuiItems.AddLast(SystemInterface);
            _panels.AddLast(SystemInterface);

            TopInterface = new TopGui();
            _allGuiItems.AddLast(TopInterface);

            BottomInterface = new BottomGui();
            _allGuiItems.AddLast(BottomInterface);

            ColumnInterface = new ColumnGui();
            //ColumnInterface.IsShow = false;
            _allGuiItems.AddLast(ColumnInterface);

            MagicInterface = new MagicGui();
            _allGuiItems.AddLast(MagicInterface);
            _panels.AddLast(MagicInterface);

            //XiuLianInterface = new XiuLianGui();
            //_allGuiItems.AddLast(XiuLianInterface);
            //_panels.AddLast(XiuLianInterface);

            GoodsInterface = new GoodsGui();
            _allGuiItems.AddLast(GoodsInterface);
            _panels.AddLast(GoodsInterface);
            //_canActivedItems.Add(GoodsInterface._listView);

            BuyInterface = new BuyGui();
            _allGuiItems.AddLast(BuyInterface);
            _panels.AddLast(BuyInterface);
            //_canActivedItems.Add(BuyInterface._listView);
            //_canActivedItems.Add(MagicInterface._listView);

            TimerInterface = new TimerGui();
            _allGuiItems.AddLast(TimerInterface);

            LittleMapInterface = new LittleMapGui();
            _allGuiItems.AddLast(LittleMapInterface);

            MemoInterface = new MemoGui();
            _allGuiItems.AddLast(MemoInterface);
            _panels.AddLast(MemoInterface);

            StateInterface = new StateGui();
            _allGuiItems.AddLast(StateInterface);
            _panels.AddLast(StateInterface);

            EquipInterface = new EquipGui();
            _allGuiItems.AddLast(EquipInterface);
            _panels.AddLast(EquipInterface);

            gambleGui = new GambleGui();
            _allGuiItems.AddLast(gambleGui);

            //WorldMapInterface = new WorldMapGui();
            //_allGuiItems.AddLast(WorldMapInterface);


            var toolTipUseType = int.Parse(Setttings.Sections["ToolTip_Use_Type"]["UseType"]);
            if (toolTipUseType == 1)
            {
                ToolTipInterface = new ToolTipGuiType1();
            }
            else
            {
                ToolTipInterface = new ToolTipGuiType2();
            }
            _allGuiItems.AddLast(ToolTipInterface);
            _panels.AddLast(ToolTipInterface);

            if (!Globals.IsMobile)
            {
                MouseInterface = new MouseGui(); 
            }
            else
            {
                TouchPad = new VirtualGamePad(Globals.TheGame.GraphicsDevice);
                TouchPad.IsVisible = true;
                TouchPad.UpdateTransformMatrix(Globals.ScaleMatrix);
            }

            MessageInterface = new MessageGui();
            _allGuiItems.AddLast(MessageInterface);
            _panels.AddLast(MessageInterface);

            DialogInterface = new DialogGui();
            _allGuiItems.AddLast(DialogInterface);

            LittleHeadInterface = new LittleHeadGui();
            _allGuiItems.AddLast(LittleHeadInterface);

            SelectionInterface = new SelectionGui();
            _allGuiItems.AddLast(SelectionInterface);

            OptionInterface = new OptionGui();
            _allGuiItems.AddLast(OptionInterface);

            OptionInterface.ScaleChanged += GameScaleChanged;

            MagicListManager.RenewList();
            GoodsListManager.RenewList();
        }

        private static void GameScaleChanged(object arg1, EventArgs args)
        {
            SystemInterface.RePosition();
            TopInterface.RePosition();
            ColumnInterface.RePosition();
            BottomInterface.RePosition();
            TouchPad?.UpdateTransformMatrix(Globals.ScaleMatrix);
            TouchPad?.RePosition();
        }

        public static void Load(string magicListPath, string goodsListPath, string memoListPath)
        {
            MagicListManager.LoadList(magicListPath);
            GoodsListManager.LoadList(goodsListPath);
            MemoListManager.LoadList(memoListPath);
        }

        public static void Save(string magicListPath, string goodsListPath, string memoListPath)
        {
            MagicListManager.SaveList(magicListPath);
            GoodsListManager.SaveList(goodsListPath);
            MemoListManager.SaveList(memoListPath);
        }

        public static void PlayInterfaceShowMissSound(bool isShow)
        {
            if (isShow)
            {
                if (_interfaceShow != null) _interfaceShow.Play();
            }
            else
            {
                if(_interfaceMiss != null) _interfaceMiss.Play();
            }
             
        }

        public static void ToggleMagicGuiShow()
        {
            PlayInterfaceShowMissSound(!MagicInterface.IsShow);
            if (MagicInterface.IsShow)
                MagicInterface.IsShow = false;
            else
            {
                MagicInterface.IsShow = true;
                GoodsInterface.IsShow = false;
                MemoInterface.IsShow = false;
            }
        }

        public static void ToggleGoodsGuiShow()
        {
            PlayInterfaceShowMissSound(!GoodsInterface.IsShow);
            if (GoodsInterface.IsShow)
            {
                GoodsInterface.IsShow = false;

                _canActivedItems.Remove(GoodsInterface._listView);
            }
            else
            {
                GoodsInterface.IsShow = true;
                //                MagicInterface.IsShow = false;
                MemoInterface.IsShow = false;

                _canActivedItems.Add(GoodsInterface._listView);
            }
        }

        public static void ToggleMemoGuiShow()
        {
            PlayInterfaceShowMissSound(!MemoInterface.IsShow);
            if (MemoInterface.IsShow)
                MemoInterface.IsShow = false;
            else
            {
                MemoInterface.IsShow = true;
                GoodsInterface.IsShow = false;
//                MagicInterface.IsShow = false;
            }
        }

		/*
        public static void ToggleXiuLianGuiShow()
        {
            PlayInterfaceShowMissSound(!XiuLianInterface.IsShow);
            if (XiuLianInterface.IsShow)
                XiuLianInterface.IsShow = false;
            else
            {
                XiuLianInterface.IsShow = true;
                StateInterface.IsShow = false;
                EquipInterface.IsShow = false;
            }
        }
		*/

        public static void ToggleStateGuiShow()
        {
            PlayInterfaceShowMissSound(!StateInterface.IsShow);
            if (StateInterface.IsShow)
                StateInterface.IsShow = false;
            else
            {
                StateInterface.IsShow = true;
                //XiuLianInterface.IsShow = false;
                EquipInterface.IsShow = false;
            }
        }

        public static void ToggleEquipGuiShow()
        {
            PlayInterfaceShowMissSound(!EquipInterface.IsShow);
            if (EquipInterface.IsShow)
            {
                EquipInterface.IsShow = false;
                MagicInterface.IsShow = false;

                _canActivedItems.Remove(MagicInterface._listView);
            }
            else
            {
                EquipInterface.IsShow = true;
                MagicInterface.IsShow = true;
                //StateInterface.IsShow = false;

                _canActivedItems.Add(MagicInterface._listView);
            }
        }

        public static void UpdateMagicView()
        {
            MagicInterface.UpdateItems();
            BottomInterface.UpdateMagicItems();
            TouchPad?.UpdateMagicItems();
//            XiuLianInterface.UpdateItem();
        }

        public static void UpdateGoodsView()
        {
            GoodsInterface.UpdateItems();
            BottomInterface.UpdateGoodsItems();
            EquipInterface.UpdateItems();
        }

        public static void UpdateGoodItemView(int listIndex)
        {
            if (GoodsListManager.IsInStoreRange(listIndex))
            {
                GoodsInterface.UpdateListItem(listIndex);
            }
            else if (GoodsListManager.IsInBottomGoodsRange(listIndex))
            {
                BottomInterface.UpdateGoodItem(listIndex);
            }
        }

        public static void UpdateMemoView()
        {
            MemoInterface.UpdateTextShow();
        }

        public static void Show(bool isShow = true)
        {
            foreach (var item in _allGuiItems)
            {
                item.IsShow = isShow;
            }
        }

        public static void ShowAllPanels(bool show = true)
        {
            foreach (var panel in _panels)
            {
                panel.IsShow = false;
            }
        }

        public static bool HasPanelsShow()
        {
            foreach (var panel in _panels)
            {
                if (panel.IsShow) return true;
            }
            return false;
        }

        #region Functionail method

        public static void ShowTitle(bool isShow = true)
        {
            TitleInterface.IsShow = isShow;
        }

        public static void ShowSystem(bool isShow = true)
        {
            PlayInterfaceShowMissSound(isShow);
            if (isShow)
            {
                ShowAllPanels(false);
            }
            SystemInterface.IsShow = isShow;
            Globals.TheGame.IsGamePlayPaused = isShow;
        }
        public static void ShowGamble(bool isShow = true)
        {
            PlayInterfaceShowMissSound(isShow);
            if (isShow)
            {
                ShowAllPanels(false);
            }
            gambleGui.IsShow = isShow;
//            Globals.TheGame.IsGamePlayPaused = isShow;
        }

        public static void ShowOptionGui(bool isShow = true)
        {
            PlayInterfaceShowMissSound(isShow);
            //if (isShow)
            //{
            //    ShowAllPanels(false);
            //}
            OptionInterface.IsShow = isShow;
        }

        private static void ShowSaveLoad(bool isShow, bool canSave)
        {
            SaveLoadInterface.IsShow = isShow;
            SaveLoadInterface.CanSave = canSave;

            if (!isShow)
            {
                switch (GameState.State)
                {
                    case GameState.StateType.Title:
                        ScriptExecuter.ReturnToTitle();
                        break;
                    case GameState.StateType.Playing:
                        break;
                }
            }
        }

        public static void ShowSaveLoad(bool isShow = true)
        {
            ShowSaveLoad(isShow, true);
        }

        public static void ShowLoad(bool isShow = true)
        {
            ShowSaveLoad(isShow, false);
        }

        public static void ShowMessage(string message)
        {
            MessageInterface.ShowMessage(message);
        }

        public static void ShowDialog(string text, int portraitIndex = -1)
        {
            ShowAllPanels(false);
            DialogInterface.ShowText(text, portraitIndex);
        }

        public static void EndDialog()
        {
            DialogInterface.ShowText("");
            DialogInterface.IsShow = false;
        }

        public static void Selection(string message, string selectA, string selectionB)
        {
            ShowAllPanels(false);
            DialogInterface.Select(message, selectA, selectionB);
        }

        public static void ChooseEx(string message, List<string> selections)
        {
            ShowAllPanels(false);
            SelectionInterface.Select(message,selections);
        }

        public static bool IsSelectionEnd()
        {
            return !DialogInterface.IsInSelecting;
        }

        public static bool IsChooseExEnd()
        {
            return !SelectionInterface.IsInSelecting;
        }

        public static int GetSelection()
        {
            return DialogInterface.Selection;
        }

        public static int GetMultiSelectionResult()
        {
            return SelectionInterface.Selection;
        }

        public static bool IsDialogEnd()
        {
            return !DialogInterface.IsShow;
        }

        public static void AddMemo(string text)
        {
            MemoListManager.AddMemo(text);
            UpdateMemoView();
        }

        /// <summary>
        /// Use good at bottom gui.
        /// </summary>
        /// <param name="index">0-2</param>
        public static void UsingBottomGood(int index)
        {
            GoodsListManager.UsingGood(index + GoodsListManager.BottomIndexBegin);
        }

        public static void DeleteGood(string fileName)
        {
            GoodsListManager.DeleteGood(fileName);
            UpdateGoodsView();
        }

        public static void EquipGoods(int goodListIndex, Good.EquipPosition part)
        {
            Character user = null;
            if (EquipInterface.Index != Globals.PlayerIndex)
            {
                user = NpcManager.GetPartnerByName(PartnerList.GetName(EquipInterface.Index));
            }
            else
            {
                user = Globals.ThePlayer;
            }
            if (!GoodsListManager.CanEquip(goodListIndex, part, user)) return;
            EquipInterface.EquipGood(goodListIndex);
        }

        public static void BuyGoods(string listFileName, bool canSellSelfGoods)
        {
            ShowAllPanels(false);
            BuyInterface.BeginBuy(listFileName, canSellSelfGoods);
            GoodsInterface.IsShow = true;

            _canActivedItems.Add(BuyInterface._listView);
            _canActivedItems.Add(GoodsInterface._listView);
        }

        public static void EndBuyGoods()
        {
            BuyInterface.EndBuy();
            GoodsInterface.IsShow = false;

            _canActivedItems.Remove(BuyInterface._listView);
            _canActivedItems.Remove(GoodsInterface._listView);
        }

        public static bool IsBuyGoodsEnd()
        {
            return !BuyInterface.IsShow;
        }

        public static void OpenTimeLimit(int seconds)
        {
            TimerInterface.StartTimer(seconds);
        }

        public static void CloseTimeLimit()
        {
            TimerInterface.StopTimer();
        }

        public static void HideTimerWindow()
        {
            TimerInterface.HideTimerWnd();
        }

        public static bool IsTimerWindowHided()
        {
            return TimerInterface.IsHide;
        }

        public static int GetTimerCurrentSeconds()
        {
            return TimerInterface.GetCurrentSecond();
        }

        public static bool IsTimerStarted()
        {
            return TimerInterface.IsShow;
        }

        public static Vector2 GetMouseScreenPosition()
        {
            return MouseInterface != null ? MouseInterface.ScreenPosition : Vector2.Zero;
        }

        public static Vector2 GetTouchScreenPosition()
        {
            Vector2 screenPosition = Vector2.Zero;
            if (CurrentTouchState.Count > 0) // 如果是触摸事件
            {
                // 使用第一个触摸点的屏幕坐标
                screenPosition = new Vector2(GuiManager.CurrentTouchState[0].Position.X / Globals.Scale, GuiManager.CurrentTouchState[0].Position.Y / Globals.Scale);
            }
            return screenPosition;
        }

        /// <summary>
        /// Get magic item info at bottom gui.
        /// </summary>
        /// <param name="index">0-4</param>
        /// <returns>Magic item info.Return null if not found.</returns>
        public static MagicListManager.MagicItemInfo GetBottomMagicItemInfo(int index)
        {
            return MagicListManager.GetItemInfo(index + MagicListManager.BottomIndexBegin);
        }

        public static void Adjust(int windowWidth, int windowHeight)
        {
            if (BottomInterface != null)
            {
                BottomInterface.Position = new Vector2(BottomInterface.Position.X, 
                    windowHeight - BottomInterface.Height);
                UpdateGoodsView();
            }

            if (ColumnInterface != null)
            {
                ColumnInterface.Position = new Vector2(ColumnInterface.Position.X,
                    windowHeight - ColumnInterface.Height);
            }
        }

        #endregion Functionail method

        #region Handle key press
        private static bool IsKeyPressed(KeyboardState keyboardState, Keys key)
        {
            return (keyboardState.IsKeyDown(key) &&
                    Globals.TheGame.LastKeyboardState.IsKeyUp(key));
        }

        private static bool IsKeyPressedAndCanInput(KeyboardState keyboardState, Keys key)
        {
            return (IsKeyPressed(keyboardState, key) &&
                    !Globals.IsInputDisabled &&
                    !ScriptManager.IsInRunningScript);
        }

        private static bool IsShowLittleMapKeyPressed(KeyboardState keyboardState)
        {
            return (!Globals.IsInputDisabled &&
                    keyboardState.IsKeyDown(Keys.Tab) &&
                    Globals.TheGame.LastKeyboardState.IsKeyUp(Keys.Tab));
        }

        private static bool IsShowWorldMapKeyPressed(KeyboardState keyboardState)
        {
            return (!Globals.IsInputDisabled &&
                    keyboardState.IsKeyDown(Keys.M) &&
                    Globals.TheGame.LastKeyboardState.IsKeyUp(Keys.M));
        }

        /// <summary>
        /// 赌博
        /// </summary>
        /// <param name="keyboardState"></param>
        /// <returns></returns>
        private static bool IsShowGambleKeyPressed(KeyboardState keyboardState)
        {
            return (!Globals.IsInputDisabled &&
                    keyboardState.IsKeyDown(Keys.F11) &&
                    Globals.TheGame.LastKeyboardState.IsKeyUp(Keys.F11));
        }
        #endregion Handle key press

        public static void Update(GameTime gameTime)
        {
            var keyboardState = Keyboard.GetState();
            var mouseState = Mouse.GetState();
            var gamepadState = GamePad.GetState(PlayerIndex.One);

            MouseInterface?.Update(gameTime);
            if (!Globals.IsTouchPadDisabled && !Globals.IsInputDisabled)
            {
                TouchPad?.Update(gameTime, CurrentTouchState); 
            }
            ColumnInterface.Update(gameTime);

            //check mouse state
            if (
                IsMouseStateEated &&
                    (
                    mouseState.LeftButton == ButtonState.Pressed ||
                    mouseState.RightButton == ButtonState.Pressed ||
                    mouseState.MiddleButton == ButtonState.Pressed
                    )
                )
            {
                IsMouseStateEated = true;
            }
            else IsMouseStateEated = false;

            if (SaveLoadInterface.IsShow)
            {
                //Temporaty enable input
                Globals.EnableInputTemporary();
                SaveLoadInterface.Update(gameTime);
                if (IsKeyPressedAndCanInput(keyboardState, Keys.Escape)
                    || IsGamepadPressed(gamepadState, Buttons.B))
                {
                    ShowSaveLoad(false);
                }
                //Restore input
                Globals.RestoreInputDisableState();
            }
            else if (TitleInterface.IsShow)
            {
                //Temporaty enable input
                Globals.EnableInputTemporary();
                TitleInterface.Update(gameTime);
                //Restore input
                Globals.RestoreInputDisableState();
            }
            else if (OptionInterface.IsShow)
            {
                Globals.EnableInputTemporary();
                OptionInterface.Update(gameTime);
                if (IsKeyPressedAndCanInput(keyboardState, Keys.Escape)
                    || IsGamepadPressed(gamepadState, Buttons.B))
                {
                    ShowOptionGui(false);
                }
                Globals.RestoreInputDisableState();
            }
            else if (SystemInterface.IsShow)
            {
                //Temporaty enable input
                Globals.EnableInputTemporary();
                SystemInterface.Update(gameTime);
                if (IsKeyPressedAndCanInput(keyboardState, Keys.Escape)
                    || IsGamepadPressed(gamepadState, Buttons.B))
                {
                    ShowSystem(false);
                }
                //Restore input
                Globals.RestoreInputDisableState();
            }
            else if (LittleMapInterface.IsShow)
            {
                //Temporaty enable input
                Globals.EnableInputTemporary();
                LittleMapInterface.Update(gameTime);
                if (IsShowLittleMapKeyPressed(keyboardState) ||
                    IsKeyPressedAndCanInput(keyboardState, Keys.Escape)
                    || IsGamepadPressed(gamepadState, Buttons.LeftStick))
                {
                    LittleMapInterface.IsShow = false;
                }
                //Restore input
                Globals.RestoreInputDisableState();
            }
            /*
            else if (WorldMapInterface.IsShow)
            {
                //Temporaty enable input
                Globals.EnableInputTemporary();
                WorldMapInterface.Update(gameTime);
                if (IsShowWorldMapKeyPressed(keyboardState) ||
                    IsKeyPressedAndCanInput(keyboardState, Keys.Escape))
                {
                    WorldMapInterface.IsShow = false;
                }
                //Restore input
                Globals.RestoreInputDisableState();
            }*/
            else if (gambleGui.IsShow) //赌博测试
            {

                Globals.EnableInputTemporary();
                gambleGui.Update(gameTime);

                Globals.RestoreInputDisableState();
            }
            else if (SelectionInterface.IsShow)
            {
                //Temporaty enable input
                Globals.EnableInputTemporary();

                IsMouseStateEated = true;
                SelectionInterface.Update(gameTime);

                //Restore input
                Globals.RestoreInputDisableState();
            }
            else if (DialogInterface.IsShow)
            {
                //Temporaty enable input
                Globals.EnableInputTemporary();

                IsMouseStateEated = true;
                if (DialogInterface.IsInSelecting)
                {
                    DialogInterface.Update(gameTime);
                    //Check wheathe selection ended after updated
                    if (!DialogInterface.IsInSelecting)
                    {
                        DialogInterface.IsShow = false;
                    }
                }
                else
                {
                    DialogInterface.Update(gameTime);
                    if (IsTalkNext())
                    {
                        if (!DialogInterface.NextPage())
                            DialogInterface.IsShow = false;
                    }
                }
                //Restore input
                Globals.RestoreInputDisableState();
            }
            else
            {
                if (BuyInterface.IsShow)
                {
                    //Temporaty enable input
                    Globals.EnableInputTemporary();

                    BuyInterface.Update(gameTime);
                    GoodsInterface.Update(gameTime);
                    BottomInterface.Update(gameTime);
                    IsMouseStateEated = true;

                    if (IsKeyPressed(keyboardState, Keys.Escape)
                        || IsGamepadPressed(gamepadState, Buttons.B))
                    {
                        EndBuyGoods();
                        ShowAllPanels(false);
                    }
                    //Restore input
                    Globals.RestoreInputDisableState();
                }
                else
                {
                    if (IsShowLittleMapKeyPressed(keyboardState)
                        || IsGamepadPressed(gamepadState, Buttons.Start))
                    {
                        ShowAllPanels(false);
                        LittleMapInterface.IsShow = true;
                    }
                    /*
                    if (IsShowWorldMapKeyPressed(keyboardState))
                    {
                        ShowAllPanels(false);
                        WorldMapInterface.IsShow = true;
                    }*/
                    if (IsKeyPressedAndCanInput(keyboardState, Keys.Escape)
                        || (gamepadState.IsButtonDown(Buttons.RightShoulder) && gamepadState.IsButtonDown(Buttons.DPadUp) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.DPadUp)))
                    {
                        if (HasPanelsShow())
                        {
                            ShowAllPanels(false);
                        }
                        else
                        {
                            ShowSystem();
                        }
                    }
                    /*
                    else if (IsKeyPressedAndCanInput(keyboardState, Keys.F1))
                    {
                        ToggleStateGuiShow();
                    }*/
                    else if (IsKeyPressedAndCanInput(keyboardState, Keys.F2)
                        || (gamepadState.IsButtonDown(Buttons.RightShoulder) && gamepadState.IsButtonDown(Buttons.DPadLeft) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.DPadLeft)))
                    {
                        ToggleEquipGuiShow();
                    }
                    /*
                    else if (IsKeyPressedAndCanInput(keyboardState, Keys.F3))
                    {
                        ToggleXiuLianGuiShow();
                    }*/
                    else if (IsKeyPressedAndCanInput(keyboardState, Keys.F3)
                        || (gamepadState.IsButtonDown(Buttons.RightShoulder) && gamepadState.IsButtonDown(Buttons.DPadRight) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.DPadRight)))
                    {
                        ToggleGoodsGuiShow();
                    }
                    else if (IsKeyPressedAndCanInput(keyboardState, Keys.F6))
                    {
                        ToggleMagicGuiShow();
                    }
                    else if (IsKeyPressedAndCanInput(keyboardState, Keys.F4)
                        || (gamepadState.IsButtonDown(Buttons.RightShoulder) && gamepadState.IsButtonDown(Buttons.DPadDown) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.DPadDown)))
                    {
                        ToggleMemoGuiShow();
                    }
                    else if (IsKeyPressedAndCanInput(keyboardState, Keys.F11))
                    {
                        Storage.Saver.QuickSave();
                    }
                    else if (IsKeyPressed(keyboardState, Keys.F12) 
                        || (gamepadState.IsButtonDown(Buttons.RightShoulder) && IsGamepadPressed(gamepadState, Buttons.LeftShoulder)))
                    {
                        Globals.TheGame.ToggleDebugMode();
                    }
                    else if (IsKeyPressedAndCanInput(keyboardState, Keys.F10)
                            || (gamepadState.IsButtonDown(Buttons.RightShoulder) && IsGamepadPressed(gamepadState, Buttons.LeftStick)))
                    {
                        Globals.EnableAutoSave = !Globals.EnableAutoSave;
                    }
                    else //增加作弊码
                    {
                        if (Globals.TheGame.IsInEditMode)
                        {
                            //K 状态满
                            //L 主角升一级
                            //M 增加钱1000  
                            if (IsKeyPressedAndCanInput(keyboardState, Keys.K)
                                || IsGamepadPressed(gamepadState, Buttons.LeftShoulder))
                            {
                                var partners = NpcManager.GetAllPartner();
                                foreach (var partner in partners)
                                {
                                    partner.FullLife();
                                    partner.FullMana();
                                    partner.FullThew();
                                }
                                Globals.ThePlayer.FullLife();
                                Globals.ThePlayer.FullMana();
                                Globals.ThePlayer.FullThew();

                            }
                            else if (IsKeyPressedAndCanInput(keyboardState, Keys.L)
                                || IsGamepadPressed(gamepadState, Buttons.DPadDown))
                            {
                                Globals.ThePlayer.LevelUp();
                            }
                            else if (IsKeyPressedAndCanInput(keyboardState, Keys.M))
                            {
                                Globals.ThePlayer.AddMoney(1000);
                            }
                            else if (IsKeyPressedAndCanInput(keyboardState, Keys.N)
                                || IsGamepadPressed(gamepadState, Buttons.Start)) //杀死所有敌人
                            {
                                NpcManager.AllEnemyDie();
                            }
                            //else if(IsKeyPressedAndCanInput(keyboardState, Keys.O)
                            //    || IsGamepadPressed(gamepadState, Buttons.LeftStick))
                            //{
                            //    Globals.EnableAutoSave = !Globals.EnableAutoSave;
                            //}
                            else if (IsKeyPressedAndCanInput(keyboardState, Keys.U))
                            {
                                int index = 0;
                                var info = GuiManager.GetBottomMagicItemInfo(index);
                                if (info != null)
                                {
                                    Globals.ThePlayer.AddMagicExp(info, info.TheMagic.LevelupExp - info.Exp + 1);
                                }
                            }
                            else if (IsKeyPressedAndCanInput(keyboardState, Keys.B)) //修正操作者的Kind属性
                            {
                                Globals.ThePlayer.Kind = (int)Character.CharacterKind.Player;
                            }
                        }
                    }
                    TopInterface.Update(gameTime);
                    BottomInterface.Update(gameTime);
                    MagicInterface.Update(gameTime);
                    //XiuLianInterface.Update(gameTime);
                    GoodsInterface.Update(gameTime);
                    MemoInterface.Update(gameTime);
                    //StateInterface.Update(gameTime);
                    EquipInterface.Update(gameTime);
                    ToolTipInterface.Update(gameTime);

                }

                //如果Gamepad按下LB键，切换到下一个可激活的界面
                if (IsGamepadPressed(gamepadState, Buttons.LeftShoulder))
                {
                    bool hasActived = false;
                    for (int i = 0; i < _canActivedItems.Count; i++)
                    {
                        if (_canActivedItems[i].Actived)
                        {
                            hasActived = true;
                            for (int j = 1; j < _canActivedItems.Count; j++)
                            {
                                var index = (i + j) % _canActivedItems.Count;
                                var item = _canActivedItems[index] as GuiItem;
                                if (item != null && item.IsShow)
                                {
                                    _canActivedItems[i].Actived = false;
                                    _canActivedItems[index].Actived = true;
                                    break;
                                }
                            }
                            break;
                        }
                    }
                    //如果没有激活的界面，激活第一个
                    if (!hasActived && _canActivedItems.Count > 0)
                    {
                        for (int i = 0; i < _canActivedItems.Count; i++)
                        {
                            var item = _canActivedItems[i] as GuiItem;
                            if (item != null && item.IsShow)
                            {
                                _canActivedItems[i].Actived = true;
                                break;
                            }
                        }
                    }
                }

                if (!Globals.TheGame.IsGamePlayPaused)
                {
                    TimerInterface.Update(gameTime);
                }

                if (IsDropped)
                {
                    IsDropped = false;
                    if (DragDropSourceItem != null)
                    {
                        DragDropSourceItem.IsShow = true;
                    }
                    if (DragDropSourceTexture != null &&
                        DragDropSourceTexture.Data != null &&
                        _dropSound != null)
                    {
                        _dropSound.Play();
                    }
                    DragDropSourceItem = null;
                    DragDropSourceTexture = null;
                }
            }

            MessageInterface.Update(gameTime);

            _lastMouseState = mouseState;
            _lastKeyboardState = keyboardState;
        }

        private static bool IsGamepadPressed(GamePadState gamepadState, Buttons button)
        {
            return (gamepadState.IsButtonDown(button) && Globals.TheGame.LastGamepadState.IsButtonUp(button));
        }

        public static bool CheckClick(KeyboardState keyboard, GamePadState gamePad)
        {
            //手柄点击A，键盘按Enter，等同于鼠标左键点击
            if ((keyboard.IsKeyDown(Keys.Enter) && Globals.TheGame.LastKeyboardState.IsKeyUp(Keys.Enter))
                || (gamePad.IsButtonDown(Buttons.A) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.A)))
            {
                return true;
            }
            return false;
        }

        public static void Draw(SpriteBatch spriteBatch)
        {
            if(!IsShow) return;

            if (SaveLoadInterface.IsShow)
            {
                SaveLoadInterface.Draw(spriteBatch);
            }
            else if (TitleInterface.IsShow)
            {
                TitleInterface.Draw(spriteBatch);
            }
            else
            {
                TimerInterface.Draw(spriteBatch);
                TopInterface.Draw(spriteBatch);
                MagicInterface.Draw(spriteBatch);
                //XiuLianInterface.Draw(spriteBatch);
                GoodsInterface.Draw(spriteBatch);
                BuyInterface.Draw(spriteBatch);
                MemoInterface.Draw(spriteBatch);
                StateInterface.Draw(spriteBatch);
                EquipInterface.Draw(spriteBatch);

                //ColumnInterface.Draw(spriteBatch);
                // 确保ColumnInterface在适当位置渲染
                if (ColumnInterface.IsShow)
                {
                    // 保存当前SpriteBatch状态
                    spriteBatch.End();
                    spriteBatch.Begin(SpriteSortMode.Deferred, null, transformMatrix: Globals.ScaleMatrix);

                    ColumnInterface.Draw(spriteBatch);

                    // 恢复SpriteBatch状态
                    spriteBatch.End();
                    spriteBatch.Begin(SpriteSortMode.Deferred, null, transformMatrix: Globals.ScaleMatrix);
                }

                BottomInterface.Draw(spriteBatch);
                ToolTipInterface.Draw(spriteBatch);
                MessageInterface.Draw(spriteBatch);
                DialogInterface.Draw(spriteBatch);
                SelectionInterface.Draw(spriteBatch);

                SystemInterface.Draw(spriteBatch);

                //WorldMapInterface.Draw(spriteBatch);
                LittleMapInterface.Draw(spriteBatch);
                gambleGui.Draw(spriteBatch);
                LittleHeadInterface.Draw(spriteBatch);

                OptionInterface.Draw(spriteBatch);

                if (!Globals.IsTouchPadDisabled && !Globals.IsInputDisabled)
                {
                    TouchPad?.Draw(spriteBatch); 
                }
            }

            MouseInterface?.Draw(spriteBatch);
        }
    }
}