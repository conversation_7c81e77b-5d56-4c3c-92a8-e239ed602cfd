﻿using System;
using System.Collections.Generic;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Audio;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input;
using Microsoft.Xna.Framework.Input.Touch;

namespace Engine.Gui.Base
{
    public class GuiItem
    {
        private MouseState _lastMouseState;
        private bool _isShow = true;
        private bool _isClicked;
        private bool _isRightClicked;
        private bool _isStayOver;
        private float _stayOverMilliSecond;
        public event Action<object, MouseEvent> MouseLeave;
        public event Action<object, MouseEvent> MouseEnter; 
        public event Action<object, MouseEvent> MouseStayOver;
        public event Action<object, MouseMoveEvent> MouseMove;
        public event Action<object, MouseLeftDownEvent> MouseLeftDown;
        public event Action<object, MouseLeftUpEvent> MouseLeftUp;
        public event Action<object, MouseLeftClickingEvent> MouseLeftClicking;
        public event Action<object, MouseRightDownEvent> MouseRightDown;
        public event Action<object, MouseRightUpEvent> MouseRightUp;
        public event Action<object, MouseLeftClickEvent> Click;
        public event Action<object, MouseRightClickEvent> RightClick;
        public event Action<object, MouseEvent> MouseScrollUp;
        public event Action<object, MouseEvent> MouseScrollDown;
        public event Action<object, GameTime> OnUpdate;
       

        public virtual bool IsShow
        {
            get { return _isShow; }
            set { _isShow = value; }
        }

        public bool IsClicked
        {
            get { return _isClicked; }
            set
            {
                _isClicked = value;
                if (value) IsMouveOver = false;
            }
        }

        public bool IsRightClicked
        {
            get { return _isRightClicked; }
            set
            {
                _isRightClicked = value;
            }
        }
        public bool IsTapClicked { get; protected set; }

        public bool IsFocus { get; set; }

        /// <summary>
        /// Is mouse on top of this gui item.
        /// </summary>
        public bool InRange { get; set; }

        public Vector2 ScreenPosition
        {
            get
            {
                if (Parent != null)
                {
                    return Parent.ScreenPosition + Position;
                }
                return Position;
            }
        }

        public Vector2 CenterScreenPosition
        {
            get { return ScreenPosition + new Vector2(Width/2f, Height/2f); }
        }

        public Vector2 Position { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public Texture BaseTexture { get; set; }
        public Texture MouseOverTexture { get; set; }
        public Texture ClickedTexture { get; set; }
        public SoundEffect EnteredSound { get; set; }
        public SoundEffect ClickedSound { get; set; }
        public bool IsMouveOver { get; set; }
        public GuiItem Parent { get; set; }

        public virtual Rectangle RegionInScreen
        {
            get
            {
                return new Rectangle((int)ScreenPosition.X,
                    (int)ScreenPosition.Y,
                    Width,
                    Height);
            }
        }

        public GuiItem() { }

        public GuiItem(
            GuiItem parent,
            Vector2 position,
            int width,
            int height,
            Texture baseTexture,
            Texture mouseOverTexture = null,
            Texture clickedTexture = null,
            SoundEffect enteredSound = null,
            SoundEffect clickedSound = null)
        {
            Parent = parent;
            Position = position;
            Width = width;
            Height = height;
            BaseTexture = baseTexture;
            MouseOverTexture = mouseOverTexture;
            ClickedTexture = clickedTexture;
            EnteredSound = enteredSound;
            ClickedSound = clickedSound;
        }

        public Vector2 ToLocalPosition(Vector2 screenPositon)
        {
            return screenPositon - ScreenPosition;
        }

        public virtual void Update(GameTime gameTime)
        {
            if (!IsShow) return;

            // 鼠标状态
            var mouseState = Mouse.GetState();
            Vector2 screenPosition = Vector2.Zero;
            bool isTouching = false;
            TouchLocation? touch = null;

            // 遍历当前帧的触摸状态
            foreach (var touchLocation in GuiManager.CurrentTouchState)
            {
                var logicPosition = new Vector2(touchLocation.Position.X / Globals.Scale, touchLocation.Position.Y / Globals.Scale);
                // 判断触摸点是否在 GUIItem 的范围内
                if (RegionInScreen.Contains((int)logicPosition.X, (int)logicPosition.Y))
                {
                    screenPosition = logicPosition;
                    touch = touchLocation;
                    isTouching = true;
                    break; // 如果找到了一个触摸点，不再继续检查其他触摸点
                }
            }

            var position = screenPosition - ScreenPosition;
            var lastPosition = new Vector2(_lastMouseState.X, _lastMouseState.Y) - ScreenPosition;

            if (OnUpdate != null)
            {
                OnUpdate(this, gameTime);
            }

            // 如果当前 GUIItem 被触摸
            if (isTouching && touch.HasValue)
            {
                var touchLocation = touch.Value;

                // 处理触摸按下事件
                if (touchLocation.State == TouchLocationState.Pressed)
                {
                    IsTapClicked = true;
                    if (ClickedTexture != null) ClickedTexture.CurrentFrameIndex = 0;

                    if (MouseLeftDown != null)
                    {
                        MouseLeftDown(this, new MouseLeftDownEvent(position, screenPosition));
                    }

                    if (ClickedSound != null) ClickedSound.Play();
                }

                // 处理长按事件
                if (touchLocation.State == TouchLocationState.Moved && !_isStayOver)
                {
                    _stayOverMilliSecond += (float)gameTime.ElapsedGameTime.TotalMilliseconds;
                    if (_stayOverMilliSecond > 500) // 长按时间阈值
                    {
                        _isStayOver = true;
                        if (MouseStayOver != null)
                        {
                            MouseStayOver(this, new MouseEvent(position, screenPosition));
                        }
                    }
                    var prevPosition = touchLocation;
                    if (touchLocation.TryGetPreviousLocation(out prevPosition))
                    {
                        if (Vector2.Distance(prevPosition.Position, touchLocation.Position) > 5)
                        {

                            if (MouseMove != null)
                            {
                                MouseMove(this,
                                    new MouseMoveEvent(position,
                                        screenPosition,
                                        true,
                                        mouseState.RightButton == ButtonState.Pressed));
                            }
                        }
                    }
                }

                // 处理触摸释放事件
                if (touchLocation.State == TouchLocationState.Released && IsTapClicked)
                {
                    if (!_isStayOver)
                    {
                        if (Click != null)
                        {
                            Click(this, new MouseLeftClickEvent(position, screenPosition));
                        }
                    }
                    else
                    {
                        if(MouseLeftUp != null)
                        {
                            MouseLeftUp(this, new MouseLeftUpEvent(position, screenPosition));
                        }
                        _isStayOver = false;
                        _stayOverMilliSecond = 0;
                    }
                    IsTapClicked = false;
                }
                //如果处理过
                GuiManager.IsTouchConsumed = true;

                InRange = true;
                IsMouveOver = true;
            }
            else
            {
                // 处理鼠标事件
                if (RegionInScreen.Contains(mouseState.X, mouseState.Y))
                {
                    screenPosition = new Vector2(mouseState.X, mouseState.Y);

                    if (InRange == false)
                    {
                        if (EnteredSound != null)
                        {
                            EnteredSound.Play();
                        }
                        if (MouseEnter != null)
                        {
                            MouseEnter(this, new MouseEvent(position, screenPosition));
                        }
                    }

                    if (lastPosition == position && !_isStayOver)
                    {
                        _stayOverMilliSecond += (float)gameTime.ElapsedGameTime.TotalMilliseconds;
                        if (_stayOverMilliSecond > 500)
                        {
                            _isStayOver = true;
                            if (MouseStayOver != null)
                            {
                                MouseStayOver(this, new MouseEvent(position, screenPosition));
                            }
                        }
                    }

                    if (mouseState.LeftButton == ButtonState.Pressed &&
                        _lastMouseState.LeftButton == ButtonState.Released)
                    {
                        IsClicked = true;
                        if (ClickedTexture != null) ClickedTexture.CurrentFrameIndex = 0;
                        if (MouseLeftDown != null)
                        {
                            MouseLeftDown(this, new MouseLeftDownEvent(position, screenPosition));
                        }

                        if (ClickedSound != null) ClickedSound.Play();
                    }

                    if (mouseState.LeftButton == ButtonState.Released &&
                        _lastMouseState.LeftButton == ButtonState.Pressed &&
                        IsClicked)
                    {
                        if (Click != null)
                        {
                            Click(this, new MouseLeftClickEvent(position, screenPosition));
                        }
                        IsClicked = false;
                    }

                    if (mouseState.RightButton == ButtonState.Pressed &&
                        _lastMouseState.RightButton == ButtonState.Released)
                    {
                        IsRightClicked = true;
                        if (MouseRightDown != null)
                        {
                            MouseRightDown(this, new MouseRightDownEvent(position, screenPosition));
                        }

                        if (RightClick != null)
                        {
                            RightClick(this, new MouseRightClickEvent(position, screenPosition));
                        }
                    }

                    if (MouseScrollHandler.IsScrollUp &&
                        MouseScrollUp != null)
                    {
                        MouseScrollUp(this, new MouseEvent(position, screenPosition));
                    }
                    if (MouseScrollHandler.IsScrollDown &&
                        MouseScrollDown != null)
                    {
                        MouseScrollDown(this, new MouseEvent(position, screenPosition));
                    }

                    InRange = true;
                    IsMouveOver = true;
                }
                else
                {
                    if (InRange)
                    {
                        if (MouseLeave != null)
                        {
                            MouseLeave(this, new MouseEvent(position, ScreenPosition));
                        }
                    }
                    InRange = false;
                    IsMouveOver = false;
                    _isStayOver = false;
                    _stayOverMilliSecond = 0;
                }

                if (mouseState.LeftButton == ButtonState.Released &&
                    _lastMouseState.LeftButton == ButtonState.Pressed)
                {
                    if (MouseLeftUp != null)
                    {
                        MouseLeftUp(this, new MouseLeftUpEvent(position, screenPosition));
                    }

                    IsClicked = false;
                }

                if (mouseState.RightButton == ButtonState.Released &&
                    _lastMouseState.RightButton == ButtonState.Pressed)
                {
                    if (MouseRightUp != null)
                    {
                        MouseRightUp(this, new MouseRightUpEvent(position, screenPosition));
                    }

                    IsRightClicked = false;
                }

                if (lastPosition != position)
                {
                    if (MouseMove != null)
                    {
                        MouseMove(this,
                            new MouseMoveEvent(position,
                                screenPosition,
                                mouseState.LeftButton == ButtonState.Pressed,
                                mouseState.RightButton == ButtonState.Pressed));
                    }
                }

                if (IsClicked)
                {
                    if (MouseLeftClicking != null)
                    {
                        MouseLeftClicking(this, new MouseLeftClickingEvent(position, screenPosition));
                    }
                }

                if (IsClicked || IsRightClicked)
                    GuiManager.IsMouseStateEated = true;
            }

            if (IsClicked && ClickedTexture != null)
                ClickedTexture.Update(gameTime);
            else if (IsMouveOver && MouseOverTexture != null)
                MouseOverTexture.Update(gameTime);
            else if (BaseTexture != null)
                BaseTexture.Update(gameTime);

            _lastMouseState = mouseState;
        }

        public virtual void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;

            if (IsClicked && ClickedTexture != null)
            {
                ClickedTexture.Draw(spriteBatch, ScreenPosition);
            }
            else if (IsMouveOver && MouseOverTexture != null)
            {
                MouseOverTexture.Draw(spriteBatch, ScreenPosition);
            }
            else if (IsFocus) // 如果元素是焦点,绘制外边框。手柄支持特性
            {
                if (MouseOverTexture != null)
                {
                    MouseOverTexture.Draw(spriteBatch, ScreenPosition);
                }
                else
                {
                    if (BaseTexture != null)
                    {
                        // 如果元素是焦点，绘制外边框
                        var borderThickness = 2; // 外边框厚度
                        var borderColor = Color.Yellow; // 外边框颜色
                        var borderRectangle = new Rectangle(
                            (int)ScreenPosition.X - borderThickness,
                            (int)ScreenPosition.Y - borderThickness,
                            BaseTexture.Width + borderThickness * 2,
                            BaseTexture.Height + borderThickness * 2
                        );

                        DrawBorder(spriteBatch, borderRectangle, borderThickness, borderColor);
                        BaseTexture.Draw(spriteBatch, ScreenPosition);
                    }
                }
            }
            else if (BaseTexture != null)
            {
                BaseTexture.Draw(spriteBatch, ScreenPosition);
            }
        }

        /// <summary>
        /// 绘制一个矩形边框
        /// </summary>
        private void DrawBorder(SpriteBatch spriteBatch, Rectangle rectangle, int thickness, Color color)
        {
            // 确保 Texture2D 为白色
            if (_borderTexture == null)
            {
                _borderTexture = new Texture2D(spriteBatch.GraphicsDevice, 1, 1);
                _borderTexture.SetData(new[] { Color.White });
            }

            // 绘制四条边
            spriteBatch.Draw(_borderTexture, new Rectangle(rectangle.Left, rectangle.Top, rectangle.Width, thickness), color); // 上边
            spriteBatch.Draw(_borderTexture, new Rectangle(rectangle.Left, rectangle.Bottom - thickness, rectangle.Width, thickness), color); // 下边
            spriteBatch.Draw(_borderTexture, new Rectangle(rectangle.Left, rectangle.Top, thickness, rectangle.Height), color); // 左边
            spriteBatch.Draw(_borderTexture, new Rectangle(rectangle.Right - thickness, rectangle.Top, thickness, rectangle.Height), color); // 右边
        }

        // 在类中添加一个静态变量用于缓存边框纹理
        private static Texture2D _borderTexture;

        protected internal void InvokeClick(object value, MouseLeftClickEvent mouseLeftClickEvent)
        {
            if (Click != null)
            {
                if (value == null)
                    value = this;
                Click(value, mouseLeftClickEvent);
            }
        }

        #region EventArgs class

        public class MouseEvent : EventArgs
        {
            public Vector2 MousePosition { private set; get; }
            public Vector2 MouseScreenPosition { private set; get; }

            public MouseEvent(Vector2 mousePosition, Vector2 mouseScreenPosition)
            {
                MousePosition = mousePosition;
                MouseScreenPosition = mouseScreenPosition;
            }
        }
        public class MouseMoveEvent : MouseEvent
        {

            public bool LeftDown { private set; get; }
            public bool RightDown { private set; get; }

            public MouseMoveEvent(Vector2 mousePosition,
                Vector2 mouseScreenPosition,
                bool leftDown,
                bool rightDown)
                : base(mousePosition, mouseScreenPosition)
            {
                LeftDown = leftDown;
                RightDown = rightDown;
            }
        }

        public class MouseLeftDownEvent : MouseEvent
        {
            public MouseLeftDownEvent(Vector2 mousePosition, Vector2 mouseScreenPosition)
                : base(mousePosition, mouseScreenPosition)
            {
            }
        }

        public class MouseLeftUpEvent : MouseEvent
        {
            public MouseLeftUpEvent(Vector2 mousePosition, Vector2 mouseScreenPosition)
                : base(mousePosition, mouseScreenPosition)
            {
            }
        }

        public class MouseRightDownEvent : MouseEvent
        {
            public MouseRightDownEvent(Vector2 mousePosition, Vector2 mouseScreenPosition)
                : base(mousePosition, mouseScreenPosition)
            {
            }
        }

        public class MouseRightUpEvent : MouseEvent
        {
            public MouseRightUpEvent(Vector2 mousePosition, Vector2 mouseScreenPosition)
                : base(mousePosition, mouseScreenPosition)
            {
            }
        }

        public class MouseLeftClickEvent : MouseEvent
        {
            public MouseLeftClickEvent(Vector2 mousePosition, Vector2 mouseScreenPosition)
                : base(mousePosition, mouseScreenPosition)
            {
            }
        }

        public class MouseLeftClickingEvent : MouseEvent
        {
            public MouseLeftClickingEvent(Vector2 mousePosition, Vector2 mouseScreenPosition)
                : base(mousePosition, mouseScreenPosition)
            {
            }
        }

        public class MouseRightClickEvent : MouseEvent
        {
            public MouseRightClickEvent(Vector2 mousePosition, Vector2 mouseScreenPosition)
                : base(mousePosition, mouseScreenPosition)
            {
            }
        }
        #endregion EventArgs class
    }
}