﻿using System;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;

namespace Engine.Gui.Base
{
    public class ColumnView : GuiItem
    {
        public float Percent { set; get; }

        public ColumnView(GuiItem parent, Vector2 position, int width, int height, Texture baseTexture, float percent = 1f)
            : base(parent, position, width, height, null)
        {
            Percent = percent;
            BaseTexture = baseTexture;
            Initialize();
        }

        private Texture2D[] _prerenderedTextures;
        private const int PRERENDER_STEPS = 50; // 预渲染不同透明度的纹理

        public void Initialize()
        {
            if (BaseTexture != null && BaseTexture.CurrentTexture != null)
            {
                var sourceTexture = BaseTexture.CurrentTexture;
                _prerenderedTextures = new Texture2D[PRERENDER_STEPS + 1];

                // 预渲染不同透明度的纹理
                for (int i = 0; i <= PRERENDER_STEPS; i++)
                {
                    float percent = i / (float)PRERENDER_STEPS;
                    _prerenderedTextures[i] = PreRenderTransparentTexture(sourceTexture, percent);
                    Console.WriteLine($"Pre-rendered texture at step {i} with percent {percent}");
                }
            }
        }

        private Texture2D PreRenderTransparentTexture(Texture2D sourceTexture, float opaquePercent)
        {
            int width = sourceTexture.Width, height = sourceTexture.Height;
            var data = new Color[width * height];
            sourceTexture.GetData(data);
            var tex = new Texture2D(sourceTexture.GraphicsDevice, width, height);
            var transHeight = (int)(sourceTexture.Height * (1 - opaquePercent));

            // 复制原始数据
            Array.Copy(data, 0, data, 0, data.Length);

            // 设置顶部区域为透明
            for (var h = 0; h < transHeight; h++)
            {
                int rowStart = h * width;
                for (var w = 0; w < width; w++)
                {
                    data[h * width + w] *= 0;
                }
            }

            tex.SetData(data);
            return tex;
        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;
            if (_prerenderedTextures != null)
            {
                // 找到最接近当前百分比的预渲染纹理
                int index = (int)Math.Round(Percent * PRERENDER_STEPS);
                index = Math.Max(0, Math.Min(PRERENDER_STEPS, index));

                if (_prerenderedTextures[index] != null)
                {
                    spriteBatch.Draw(_prerenderedTextures[index], ScreenPosition, Color.White);
                    Console.WriteLine($"Drawing prerendered texture at index {index} with percent {Percent}");
                }
                else
                {
                    Console.WriteLine($"Warning: Prerendered texture at index {index} is null.");
                }
            }
            else if (BaseTexture != null && BaseTexture.CurrentTexture != null)
            {
                // 如果没有预渲染纹理，直接绘制原始纹理
                spriteBatch.Draw(BaseTexture.CurrentTexture, ScreenPosition, Color.White);
                Console.WriteLine("Drawing base texture directly without prerendering.");
            }
            else
            {
                Console.WriteLine("Warning: Base texture or current texture is null.");
            }
        }

        // 在不再需要时释放资源
        //protected override void Dispose(bool disposing)
        //{
        //    if (disposing && _prerenderedTextures != null)
        //    {
        //        for (int i = 0; i < _prerenderedTextures.Length; i++)
        //        {
        //            if (_prerenderedTextures[i] != null)
        //            {
        //                _prerenderedTextures[i].Dispose();
        //                _prerenderedTextures[i] = null;
        //            }
        //        }
        //        _prerenderedTextures = null;
        //    }
        //    base.Dispose(disposing);
        //}
    }
}