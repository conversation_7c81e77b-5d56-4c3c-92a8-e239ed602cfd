﻿using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Audio;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Engine.Gui.Base
{
    public class GuiAnimItem : GuiItem
    {
        //public new AnimeTexture BaseTexture { get; set; }

        private float _layerDepth = 0.5f;

        public float layerDepth
        {
            get { return _layerDepth; }
            set
            {
                _layerDepth = value;
            }
        }
        public void PlayCount(int count)
        {
            ((AnimeTexture)BaseTexture).PlayCount(count);
        }
        public void PlayOneFrame(int frame)
        {
            ((AnimeTexture)BaseTexture).PlayOneFrame(frame);
        }
        public int PlayState
        {
            get
            {
                return ((AnimeTexture)BaseTexture).PlayState;
            }
            set
            {
                ((AnimeTexture)BaseTexture).PlayState = value;
            }
        }

        public bool isPlayEnd
        {
            get
            {
                return ((AnimeTexture)BaseTexture).isPlayEnd;
            }
        }

        public GuiAnimItem() : base(){ }

        public GuiAnimItem(
            GuiItem parent,
            Vector2 position,
            int width,
            int height,
            AnimeTexture baseTexture,
            Texture mouseOverTexture = null,
            Texture clickedTexture = null,
            SoundEffect enteredSound = null,
            SoundEffect clickedSound = null)
        {
            Parent = parent;
            Position = position;
            Width = width;
            Height = height;
            BaseTexture = baseTexture;
            MouseOverTexture = mouseOverTexture;
            ClickedTexture = clickedTexture;
            EnteredSound = enteredSound;
            ClickedSound = clickedSound;
        }
    }
}
