﻿using Microsoft.Xna.Framework.Media;
using Microsoft.Xna.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Engine
{
    public interface IVideoPlayer
    {
        //public IVideoPlayer GetInstance(Game game);
        public void Initialize();
        public void Play(string path, bool isAsset);
        public void Stop();
        public void Pause();
        public void Resume();
        public int GetState();
    }
}
