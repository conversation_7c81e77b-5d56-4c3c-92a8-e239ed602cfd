﻿using Engine.Gui.Base;
using Engine.Script;
using IniParser;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Texture = Engine.Gui.Base.Texture;

namespace Engine.Gui
{
    public class WorldMapGui : GuiItem
    {
        private List<WorldMapItem> allMapSenceItems;
        public WorldMapItem CurrentSelectMap { get; private set; }

        public WorldMapGui()
        {
            var cfg = GuiManager.Setttings.Sections["WorldMap"];
            if (cfg == null ) return;
            //BaseTexture = new Base.Texture(Utils.GetAsf(null, cfg["Image"]));
            using (var fs = File.Open(cfg["Image"], FileMode.Open))
            {
                BaseTexture = new Texture(new Asf(
                Texture2D.FromStream(Globals.TheGame.GraphicsDevice, fs)));
            }
            Width = BaseTexture.Width;
            Height = BaseTexture.Height;
            Position = new Vector2((Globals.WindowWidth - Width) / 2 + int.Parse(cfg["LeftAdjust"]),
                (Globals.WindowHeight - Height) / 2 + int.Parse(cfg["TopAdjust"]));

            LoadSenceMapItems();

            IsShow = false;
        }

        private void LoadSenceMapItems()
        {
            string filePath = Globals.GetRightpath(@"ini\map\worldmap.ini");
            try
            {
                if (File.Exists(filePath))
                {
                    allMapSenceItems = new List<WorldMapItem>();
                    var list = Utils.GetAllKeyDataCollection(filePath, "MAP");
                    foreach (var keyDataCollection in list)
                    {
                        var mapItem = new WorldMapItem(this, keyDataCollection);
                        mapItem.Click += MapItem_Click;
                        allMapSenceItems.Add(mapItem);
                    }
                }
                else
                {
                    Log.LogMessageToFile(filePath + " does not exists" + Environment.NewLine);
                }
            }
            catch (Exception exception)
            {
                Log.LogFileLoadError("Map name list", filePath, exception);
            }
        }

        private void MapItem_Click(object arg1, MouseLeftClickEvent arg2)
        {
            WorldMapItem item = (WorldMapItem)arg1;
            if (item != null)
            {
                if (CurrentSelectMap == item)
                {
                    ScriptExecuter.LoadMap(item.SenceMapName);
                    ScriptExecuter.SetPlayerPos(new List<string>() { item.PlayerX.ToString(), item.PlayerY.ToString() });
                    if (item.PlayerDir != default(int))
                    {
                        ScriptExecuter.SetPlayerDir(new List<string>() { item.PlayerDir.ToString() }); 
                    }
                    IsShow = false;
                }
                else
                {
                    this.CurrentSelectMap = item;
                }
            }
        }

        public override void Update(GameTime gameTime)
        {
            foreach (var mapItem in allMapSenceItems)
            {
                mapItem.Update(gameTime);
            }
            base.Update(gameTime);
        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;
            base.Draw(spriteBatch);
            foreach (var mapItem in allMapSenceItems)
            {
                mapItem.Draw(spriteBatch);
            }
        }
    }
}
