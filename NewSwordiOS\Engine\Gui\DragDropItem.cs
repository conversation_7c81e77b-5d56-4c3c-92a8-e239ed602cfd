﻿using System;
using Engine.Gui.Base;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input.Touch;
using Texture = Engine.Gui.Base.Texture;

namespace Engine.Gui
{
    public class DragDropItem : GuiItem
    {
        private TextGui TopLeftTextGui { set; get; }
        public bool UseTopLeftText { set; get; }

        public event Action<object, DragEvent> Drag;
        public event Action<object, DropEvent> Drop;
        public object Data { set; get; }

        public string TopLeftText
        {
            set
            {
                if(TopLeftTextGui == null)
                {
                    TopLeftTextGui = new TextGui(this,
                    Vector2.Zero,
                    Width,
                    Height,
                    Globals.FontSize7,
                    0,
                    0,
                    value,
                    new Color(167, 157, 255)*0.8f);
                }
                else
                {
                    TopLeftTextGui.Text = value;
                }
            }
            get
            {
                if (TopLeftTextGui == null)
                    return "";
                return TopLeftTextGui.Text;
            }
        }

        private DragDropItem() { }

        public DragDropItem(GuiItem parent,
            Vector2 position,
            int width,
            int height,
            Texture baseTexture,
            object data = null,
            bool useTopLeftText = true)
            : base(parent, position, width, height, baseTexture)
        {
            Data = data;
            UseTopLeftText = useTopLeftText;
            MouseLeftDown += delegate(object arg1, MouseLeftDownEvent arg2)
            {
                GuiManager.DragDropSourceItem = this;
                GuiManager.DragDropSourceTexture = this.BaseTexture;
                GuiManager.IsDropped = false;
                //IsShow = false;//触屏点击后消失bug
                if (Drag != null)
                {
                    Drag(this, new DragEvent(arg2.MouseScreenPosition));
                }
            };

            MouseLeftUp += delegate(object arg1, MouseLeftUpEvent arg2)
            {
                if (GuiManager.DragDropSourceItem != null)
                {
                    if (Drop != null && InRange)
                    {
                        Drop(this, new DropEvent(arg2.MouseScreenPosition, GuiManager.DragDropSourceItem));
                    }
                    GuiManager.IsDropped = true;
                }
            };
        }

        public override void Update(GameTime gameTime)
        {
            if (!IsShow) return;
            base.Update(gameTime);
            Vector2 screenPosition = Vector2.Zero;
            bool isTouching = false;
            TouchLocation? touchLoc = null;
            // 遍历当前帧的触摸状态
            foreach (var touchLocation in GuiManager.CurrentTouchState)
            {
                var logicPosition = new Vector2(touchLocation.Position.X / Globals.Scale, touchLocation.Position.Y / Globals.Scale);
                // 判断触摸点是否在 GUIItem 的范围内
                if (RegionInScreen.Contains((int)logicPosition.X, (int)logicPosition.Y))
                {
                    screenPosition = logicPosition;
                    touchLoc = touchLocation;
                    isTouching = true;
                    break; // 如果找到了一个触摸点，不再继续检查其他触摸点
                }
            }
            //foreach (var touch in GuiManager.CurrentTouchState)
            if (isTouching && touchLoc.HasValue)
            {
                //var screenPosition = new Vector2(touch.Position.X / Globals.Scale, touch.Position.Y / Globals.Scale);
                var touch = touchLoc.Value;
                // 如果拖动开始
                if (touch.State == TouchLocationState.Pressed && RegionInScreen.Contains(screenPosition.ToPoint()))
                {
                    GuiManager.DragDropSourceItem = this;
                    GuiManager.DragDropSourceTexture = this.BaseTexture;
                    GuiManager.IsDropped = false;

                    Drag?.Invoke(this, new DragEvent(screenPosition));
                    GuiManager.IsTouchConsumed = true; // 消耗触摸事件
                }

                // 如果拖动释放
                if (touch.State == TouchLocationState.Released && GuiManager.DragDropSourceItem != null && GuiManager.DragDropSourceItem != this)
                {
                    if (InRange && this.Data.GetType() == GuiManager.DragDropSourceItem.Data.GetType())
                    {
                        if (Drop != null)
                            Drop(this, new DropEvent(screenPosition, GuiManager.DragDropSourceItem));
                        GuiManager.IsDropped = true;
                        GuiManager.DragDropSourceItem = null; // 重置拖动源
                        GuiManager.DragDropSourceTexture = null;
                        GuiManager.IsTouchConsumed = true; // 消耗触摸事件
                    }
                }

                // 如果正在拖动
                if (touch.State == TouchLocationState.Moved && GuiManager.DragDropSourceItem == this)
                {
                    //this.Position = ToLocalPosition(screenPosition);
                    // 可以添加拖动中的事件处理逻辑（例如拖动预览效果）
                    GuiManager.IsTouchConsumed = true; // 消耗触摸事件
                }
            }
            if (UseTopLeftText && TopLeftTextGui != null)
                TopLeftTextGui.Update(gameTime);
        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;
            base.Draw(spriteBatch);
            if (UseTopLeftText && TopLeftTextGui != null)
                TopLeftTextGui.Draw(spriteBatch);
        }

        public abstract class DragDropEvent : EventArgs
        {
            public Vector2 MouseScreenPosition { private set; get; }

            public DragDropEvent(Vector2 mouseScreenPosition)
            {
                MouseScreenPosition = mouseScreenPosition;
            }
        }

        public class DragEvent : DragDropEvent
        {
            public DragEvent(Vector2 mouseScreenPosition)
                : base(mouseScreenPosition)
            {
            }
        }

        public class DropEvent : DragDropEvent
        {
            public DragDropItem Source { private set; get; }
            public DropEvent(Vector2 mouseScreenPosition, DragDropItem source)
                : base(mouseScreenPosition)
            {
                Source = source;
            }
        }
    }
}