﻿using System;
using System.IO;
using Microsoft.Xna.Framework.Media;
using MediaPlayer = Microsoft.Xna.Framework.Media.MediaPlayer;

namespace Engine
{
    static public class BackgroundMusic
    {
        private static string _fileName;

        public static string FileName
        {
            get { return _fileName; }
        }

        public static void Play(string fileName)
        {
            _fileName = fileName;
            if (string.IsNullOrEmpty(fileName))
            {
                //Stop music if file name is empty
                Stop();
                return;
            }
            try
            {
                var path = @"music\" + Path.GetFileNameWithoutExtension(fileName);
                path = Globals.GetMappedContentPath(path);
                var song = Globals.TheGame.Content.Load<Song>(path);
                Microsoft.Xna.Framework.Media.MediaPlayer.IsRepeating = true;
                Microsoft.Xna.Framework.Media.MediaPlayer.Play(song);
            }
            catch (Exception exception)
            {
                Log.LogFileLoadError("Music file", fileName, exception);
            }
        }

        public static void Stop()
        {
            Microsoft.Xna.Framework.Media.MediaPlayer.Stop();
            _fileName = string.Empty;
        }

        public static void Pause()
        {
            Microsoft.Xna.Framework.Media.MediaPlayer.Pause();
        }

        public static void Resume()
        {
            Microsoft.Xna.Framework.Media.MediaPlayer.Resume();
        }

        public static void SetVolume(float volume)
        {
            Microsoft.Xna.Framework.Media.MediaPlayer.Volume = volume;
        }

        public static float GetVolume()
        {
            return Microsoft.Xna.Framework.Media.MediaPlayer.Volume;
        }
    }
}
