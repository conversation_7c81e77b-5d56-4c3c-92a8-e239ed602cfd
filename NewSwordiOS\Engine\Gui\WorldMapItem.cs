﻿using Engine.Gui.Base;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using IniParser.Model;

namespace Engine.Gui
{
    public class WorldMapItem : GuiItem
    {
        public int PlayerX { get; set; }
        public int PlayerY { get; set; }

        private string mapName;

        public string SenceMapName
        {
            get { return mapName; }
            set { mapName = value; }
        }

        private string displayName;
        private KeyDataCollection keyDataCollection;

        public string ShowName
        {
            get { return displayName; }
            set { displayName = value; }
        }

        public int PlayerDir { get; private set; }

        public WorldMapItem(GuiItem parent,KeyDataCollection cfg) :
            base(parent,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])), 
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                new Base.Texture(new Asf(cfg["Image"])))//Utils.GetColor(cfg["Color"])
        {
            this.keyDataCollection = cfg;
            this.ShowName = cfg["ShowName"];
            this.SenceMapName = cfg["MapName"];
            this.PlayerX = int.Parse(cfg["PlayerX"]);
            this.PlayerY = int.Parse(cfg["PlayerY"]);
            this.PlayerDir = int.Parse(cfg["PlayerDir"]);
        }
    }
}
