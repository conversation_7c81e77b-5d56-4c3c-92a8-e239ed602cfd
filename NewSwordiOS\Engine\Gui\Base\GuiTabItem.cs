﻿using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Audio;
using Microsoft.Xna.Framework.Graphics;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Engine.Gui.Base
{
    public class GuiTabItem : GuiItem
    {
        private bool _isSelected;

        public bool IsSelected
        {
            get { return _isSelected; }
            set
            {
                _isSelected = value;
                if (value)
                {
                    TabSelected?.Invoke(this, new EventArgs());
                }
            }
        }

        private bool isEnable;

        public bool IsEnable
        {
            get { return isEnable; }
            set { isEnable = value; }
        }

        public Texture SelectedTexture { get; set; }
        public Texture EnableTexture { get; private set; }

        public event Action<object, EventArgs> TabSelected;

        public GuiTabItem() : base() { }

        public GuiTabItem(
                GuiItem parent,
                Vector2 position,
                int width,
                int height,
                Texture baseTexture,
                Texture mouseOverTexture = null,
                Texture clickedTexture = null,
                SoundEffect enteredSound = null,
                SoundEffect clickedSound = null) :
            base(parent,
                position,
                width,
                height,
                baseTexture,
                mouseOverTexture,
                clickedTexture,
                enteredSound,
                clickedSound)
        {
        }

        public GuiTabItem(
                GuiItem parent,
                Vector2 position,
                int width,
                int height,
                Texture baseTexture,
                Texture mouseOverTexture = null,
                Texture clickedTexture = null,
                Texture selectedTexture = null,
                SoundEffect enteredSound = null,
                SoundEffect clickedSound = null) :
            base(parent,
                position,
                width,
                height,
                baseTexture,
                mouseOverTexture,
                clickedTexture,
                enteredSound,
                clickedSound)
        {
            SelectedTexture = selectedTexture;
        }

        public GuiTabItem(
                GuiItem parent,
                Vector2 position,
                int width,
                int height,
                Texture baseTexture,
                Texture enableTexture,
                Texture mouseOverTexture = null,
                Texture clickedTexture = null,
                Texture selectedTexture = null,
                SoundEffect enteredSound = null,
                SoundEffect clickedSound = null) : this(parent,
                position,
                width,
                height,
                baseTexture,
                mouseOverTexture,
                clickedTexture,
                selectedTexture,
                enteredSound,
                clickedSound)
        {
            EnableTexture = enableTexture;
            SelectedTexture = selectedTexture;
        }

        public override void Update(GameTime gameTime)
        {
            base.Update(gameTime);
            if (IsSelected && SelectedTexture != null)
                SelectedTexture.Update(gameTime);
            if (IsEnable && EnableTexture != null)
                EnableTexture.Update(gameTime);
        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;

            if (IsSelected && SelectedTexture != null)
            {
                SelectedTexture.Draw(spriteBatch, ScreenPosition);
            }
            else if(IsEnable && EnableTexture != null)
            {
                EnableTexture.Draw(spriteBatch, ScreenPosition);
            }
            else if (IsMouveOver && MouseOverTexture != null)
            {
                MouseOverTexture.Draw(spriteBatch, ScreenPosition);
            }
            else if (IsClicked && ClickedTexture != null)
            {
                ClickedTexture.Draw(spriteBatch, ScreenPosition);
            }
            else if (BaseTexture != null)
            {
                BaseTexture.Draw(spriteBatch, ScreenPosition);
            }
        }
    }
}
