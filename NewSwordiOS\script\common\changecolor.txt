HideTimerWnd();
If ($Color == 1) @Color1;
If ($Color == 2) @Color2;
If ($Color == 3) @Color3;
If ($Color == 4) @Color4;
If ($Color == 5) @Color5;
If ($Color == 6) @Color6;
If ($Color == 7) @Color7;
If ($Color == 8) @Color8;
If ($Color == 9) @Color9;
If ($Color == 10) @Color10;
If ($Color == 11) @Color11;


Goto(@end);


@Color1:
ChangeMapColor(250, 250, 250);
ChangeAsfColor(255, 255, 255);
Goto(@end);

@Color2:
ChangeMapColor(225, 225, 225);
ChangeAsfColor(255, 255, 255);
Goto(@end);

@Color3:
ChangeMapColor(200, 200, 200);
ChangeAsfColor(255, 255, 255);
Goto(@end);

@Color4:
ChangeMapColor(175, 175, 175);
ChangeAsfColor(255, 255, 255);
Goto(@end);

@Color5:
ChangeMapColor(150, 150, 150);
ChangeAsfColor(255, 255, 255);
Goto(@end);

@Color6:
ChangeMapColor(125, 125, 125);
ChangeAsfColor(255, 255, 255);
Goto(@end);

@Color7:
ChangeMapColor(100, 100, 100);
ChangeAsfColor(255, 255, 255);
Goto(@end);


@Color8:
ChangeMapColor(75, 75, 75);
ChangeAsfColor(255, 255, 255);
Goto(@end);


@Color9:
ChangeMapColor(50, 50, 50);
ChangeAsfColor(255, 255, 255);
Goto(@end);


@Color10:
ChangeMapColor(25, 25, 25);
ChangeAsfColor(255, 255, 255);
Goto(@end);


@Color11:
ChangeMapColor(0, 0, 0);
ChangeAsfColor(255, 255, 255);
Goto(@end);




@end:
CloseTimeLimit();
OpenTimeLimit(2);
HideTimerWnd();
SetTimeScript(1,"changecolor.txt");
Add($Color ,1);
If ($Color >= 12 ) @Again;
Return;

@Again:
Assign($Color,1);
Return;

