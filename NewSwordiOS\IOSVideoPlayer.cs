using AVFoundation;
using AVKit;
using Engine;
using Foundation;
using System;
using UIKit;

namespace NewSwordiOS
{
    public class IOSVideoPlayer : IVideoPlayer
    {
        private AVPlayer player;
        private AVPlayerViewController controller;
        private UIViewController parentController;
        private int state = 0; // 0 = Stopped, 1 = Playing, 2 = Paused

        public IOSVideoPlayer(UIViewController parent)
        {
            parentController = parent;
        }

        public void Initialize()
        {
            // 可选：预加载资源、准备监听等
        }

        public void Play(string path, bool isAsset)
        {
            Console.WriteLine($"Playing video from path: {path}, isAsset: {isAsset}");
            var url = isAsset
                ? NSUrl.FromFilename(path)
                : NSUrl.FromString(path); // 网络/本地路径

            player = new AVPlayer(url);
            controller = new AVPlayerViewController
            {
                Player = player,
                ShowsPlaybackControls = false
            };

            // 添加点击跳过
            var tap = new UITapGestureRecognizer(() =>
            {
                Stop();
            });
            controller.View.UserInteractionEnabled = true;
            controller.View.AddGestureRecognizer(tap);

            // 监听播放完成
            NSNotificationCenter.DefaultCenter.AddObserver(
                AVPlayerItem.DidPlayToEndTimeNotification,
                (n) => Stop(),
                player.CurrentItem
            );

            parentController.PresentViewController(controller, true, () =>
            {
                player.Play();
                state = 1;
            });
        }

        public void Stop()
        {
            state = 0;

            if (player != null)
            {
                player.Pause();
                player.Dispose();
                player = null;
            }

            if (controller != null)
            {
                UIApplication.SharedApplication.InvokeOnMainThread(() =>
                {
                    controller.DismissViewController(true, () =>
                    {
                        controller.View?.RemoveFromSuperview();
                        controller.Dispose();
                        controller = null;
                    });
                });
            }
        }


        public void Pause()
        {
            player?.Pause();
            state = 2;
        }

        public void Resume()
        {
            player?.Play();
            state = 1;
        }

        public int GetState()
        {
            return state;
        }
    }
}
