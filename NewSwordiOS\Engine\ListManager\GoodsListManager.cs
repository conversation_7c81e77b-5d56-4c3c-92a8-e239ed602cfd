﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Engine.Gui;
using Engine.Gui.Base;
using Engine.Script;
using IniParser;
using IniParser.Model;

namespace Engine.ListManager
{
    public static class GoodsListManager
    {
        public static ListType Type = 0;
        public static int MaxGoods = 223;
        public static int ListIndexBegin = 1;
        public static int ListIndexEnd = 223;
        public static int StoreIndexBegin = 1;
        public static int StoreIndexEnd = 177;
        public static int EquipIndexBegin = 201;
        public static int EquipIndexEnd = 207;
        public static int BottomIndexBegin = 221;
        public static int BottomIndexEnd = 223;
        private static readonly GoodsItemInfo[] GoodsList = new GoodsItemInfo[MaxGoods + 1];

        public static int PartnerEquipIndexBegin = 180;
        public static int PartnerEquipIndexEnd = 200;

        public enum ListType
        {
            TypeByGoodType, //store goods by type
            TypeByGoodItem //store goods by item
        }

        public static void InitIndex(IniData settings)
        {
            var cfg = settings.Sections["GoodsInit"];
            Type = (ListType) int.Parse(cfg["GoodsListType"]);
            StoreIndexBegin = int.Parse(cfg["StoreIndexBegin"]);
            StoreIndexEnd = int.Parse(cfg["StoreIndexEnd"]);
            EquipIndexBegin = int.Parse(cfg["EquipIndexBegin"]);
            EquipIndexEnd = int.Parse(cfg["EquipIndexEnd"]);
            BottomIndexBegin = int.Parse(cfg["BottomIndexBegin"]);
            BottomIndexEnd = int.Parse(cfg["BottomIndexEnd"]);
            MaxGoods = Math.Max(0, StoreIndexEnd);
            MaxGoods = Math.Max(MaxGoods, EquipIndexEnd);
            MaxGoods = Math.Max(MaxGoods, BottomIndexEnd);
            ListIndexEnd = MaxGoods;
        }

        public static void RenewList()
        {
            for (var i = ListIndexBegin; i <= ListIndexEnd; i++)
            {
                GoodsList[i] = null;
            }
        }

        public static bool IndexInRange(int index)
        {
            return (index > 0 && index <= MaxGoods);
        }

        public static bool IsInEquipRange(int index)
        {
            return (index >= EquipIndexBegin && index <= EquipIndexEnd);
        }

        public static bool IsInPartnerEquipRange(int index)
        {
            return (index >= PartnerEquipIndexBegin && index <= PartnerEquipIndexEnd);
        }

        public static bool IsInStoreRange(int index)
        {
            return (index >= StoreIndexBegin && index <= StoreIndexEnd);
        }

        public static bool IsInBottomGoodsRange(int index)
        {
            return (index >= BottomIndexBegin && index <= BottomIndexEnd);
        }

        public static void LoadList(string filePath)
        {
            RenewList();
            GuiManager.UpdateGoodsView(); // clear
            //LoadGoodsFromIniFile(filePath);
            //改为异步回调
            //Action<string> action = new Action<string>(LoadGoodsFromIniFile);
            //AsyncCallback loadedCallback = new AsyncCallback(LoadGoodsFileCompleted);
            //IAsyncResult result = action.BeginInvoke(filePath, loadedCallback, "AsycState:OK");
            Task.Run(() => LoadGoodsFromIniFile(filePath)).ContinueWith(task => LoadGoodsFileCompleted());

        }

        //private static void LoadGoodsFileCompleted(IAsyncResult result)
        private static void LoadGoodsFileCompleted()
        {
            GuiManager.UpdateGoodsView();
        }

        private static void LoadGoodsFromIniFile(string filePath)
        {
            try
            {
                var parser = new FileIniDataParser();
                var data = parser.ReadFile(Globals.GetRightpath(filePath), Globals.LocalEncoding);
                foreach (var sectionData in data.Sections)
                {
                    int head;
                    if (int.TryParse(sectionData.SectionName, out head))
                    {
                        var section = data[sectionData.SectionName];
                        GoodsList[head] = new GoodsItemInfo(
                            section["IniFile"],
                            int.Parse(section["Number"]));
                    }
                }
            }
            catch (Exception exception)
            {
                RenewList();
                Log.LogFileLoadError("Goods list", filePath, exception);
            }
        }

        public static void SaveList(string filePath)
        {
            try
            {
                var data = new IniData();
                data.Sections.AddSection("Head");
                var count = 0;
                for (var i = 1; i <= MaxGoods; i++)
                {
                    var item = GoodsList[i];
                    if (item != null && item.TheGood != null)
                    {
                        count++;
                        data.Sections.AddSection(i.ToString());
                        var section = data[i.ToString()];
                        section.AddKey("IniFile", item.TheGood.FileName);
                        section.AddKey("Number", item.Count.ToString());
                    }
                }
                data["Head"].AddKey("Count", count.ToString());
                //Write to file
                string fullPath = Globals.GetRightpath(filePath);
                try
                {
                    // 确保目录结构存在
                    string directoryPath = Path.GetDirectoryName(fullPath);
                    if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
                    {
                        Directory.CreateDirectory(directoryPath);
                        Log.LogMessage("Created directory: " + directoryPath);
                    }

                    File.WriteAllText(fullPath, data.ToString(), Globals.LocalEncoding);
                }
                catch (Exception ex)
                {
                    // 记录错误信息以便调试
                    Log.LogMessage("SaveGoodsList Error: " + ex.Message);
                    Log.LogMessage("Path: " + fullPath);
                    Log.LogMessage("StackTrace: " + ex.StackTrace);
                }
            }
            catch (Exception exception)
            {
                Log.LogFileSaveError("Goods list", filePath, exception);
            }
        }

        public static void ApplyEquipSpecialEffectFromList(IEquipable player)
        {
            if (player == null) return;
            for (var i = EquipIndexBegin; i <= EquipIndexEnd; i++)
            {
                player.Equiping(Get(i), null, true);
            }
        }

        public static void RefreshEquipAllEffectFromList(IEquipable player)
        {
            if (player == null) return;
            for (var i = EquipIndexBegin; i <= EquipIndexEnd; i++)
            {
                player.Equiping(Get(i), null, false);
            }
        }

        public static void RefreshPartnerEquipEffect(IEquipable partner,int partnerStartIndex)
        {
            if (partner == null) return;
            for (var i = partnerStartIndex; i <= partnerStartIndex + 6; i++)
            {
                var good = Get(i);
                if (good != null)
                {
                    partner.Equiping(good, null, false); 
                }
            }
        }

        public static void UnEquipAllEquipWithoutTakeOff(Player player)
        {
            if (player == null) return;
            for (var i = EquipIndexBegin; i <= EquipIndexEnd; i++)
            {
                player.UnEquiping(Get(i));
            }
        }

        public static void ClearAllGoods(Player player)
        {
            UnEquipAllEquipWithoutTakeOff(player);
            RenewList();
            GuiManager.UpdateGoodsView();
        }

        /// <summary>
        /// Is magic learned from equiping equip?
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static bool IsMagicInEquipedEquip(string fileName)
        {
            for (var i = EquipIndexBegin; i <= EquipIndexEnd; i++)
            {
                var good = Get(i);
                if (good != null && Utils.EqualNoCase(good.MagicIniWhenUse, fileName))
                {
                    return true;
                }
            }
            return false;
        }

        public static void ExchangeListItemAndEquiping(int index1, int index2)
        {
            if (index1 != index2 &&
                IndexInRange(index1) &&
                IndexInRange(index2))
            {
                var temp = GoodsList[index1];
                GoodsList[index1] = GoodsList[index2];
                GoodsList[index2] = temp;
                ChangePlayerEquiping(index1, index2);
            }
        }

        /// <summary>
        /// If return true, newIndex is the new index
        /// </summary>
        /// <param name="equipItemIndex"></param>
        /// <param name="newIndex"></param>
        /// <returns></returns>
        public static bool MoveEquipItemToList(int equipItemIndex, out int newIndex)
        {
            if (IsInEquipRange(equipItemIndex) || IsInPartnerEquipRange(equipItemIndex))
            {
                var info = GoodsList[equipItemIndex];
                if (info != null)
                {
                    GoodsList[equipItemIndex] = null;
                    for (var i = StoreIndexBegin; i <= StoreIndexEnd; i++)
                    {
                        if (GoodsList[i] == null)
                        {
                            GoodsList[i] = info;
                            newIndex = i;
                            return true;
                        }
                    }
                }
            }
            newIndex = 0;
            return false;
        }

        public static bool HasFreeItemSpace()
        {
            for (var i = StoreIndexBegin; i <= StoreIndexEnd; i++)
            {
                var info = GoodsList[i];
                if (info == null)
                {
                    return true;
                }
            }

            for (var i = BottomIndexBegin; i <= BottomIndexEnd; i++)
            {
                var info = GoodsList[i];
                if (info == null)
                {
                    return true;
                }
            }
            return false;
        }

        public static bool AddGoodToList(string fileName)
        {
            int i;
            Good g;
            return AddGoodToList(fileName, out i, out g);
        }

        public static bool AddGoodToList(string fileName, out int index, out Good outGood)
        {
            index = -1;
            outGood = null;
            switch (Type)
            {
                case ListType.TypeByGoodType:
                {
                    for (var i = ListIndexBegin; i <= ListIndexEnd; i++)
                    {
                        var info = GoodsList[i];
                        if (info != null && info.TheGood != null)
                        {
                            if (Utils.EqualNoCase(info.TheGood.FileName, fileName))
                            {
                                info.Count += 1;
                                index = i;
                                outGood = info.TheGood;
                                return true;
                            }
                        }
                    }

                    for (var i = StoreIndexBegin; i <= StoreIndexEnd; i++)
                    {
                        var info = GoodsList[i];
                        if (info == null)
                        {
                            GoodsList[i] = new GoodsItemInfo(fileName, 1);
                            index = i;
                            outGood = GoodsList[i].TheGood;
                            return true;
                        }
                    }
                    }
                    break;
                case ListType.TypeByGoodItem:
                {
                    for (var i = StoreIndexBegin; i <= StoreIndexEnd; i++)
                    {
                        var info = GoodsList[i];
                        if (info == null)
                        {
                            GoodsList[i] = new GoodsItemInfo(fileName, 1);
                            index = i;
                            outGood = GoodsList[i].TheGood;
                            return true;
                        }
                    }

                    for (var i = BottomIndexBegin; i <= BottomIndexEnd; i++)
                    {
                        var info = GoodsList[i];
                        if (info == null)
                        {
                            GoodsList[i] = new GoodsItemInfo(fileName, 1);
                            index = i;
                            outGood = GoodsList[i].TheGood;
                            return true;
                        }
                    }
                }
                    break;
                default:
                    Log.LogMessage(@"GoodsListType 设置错误，请检查Content\ui\UI_Settings.ini文件");
                    return false;
            }

            GuiManager.ShowMessage("物品栏已满");

            return false;
        }

        public static GoodsItemInfo GetGoodsItemInfoFromFileName(string fileName)
        {
            for (var i = ListIndexBegin; i <= ListIndexEnd; i++)
            {
                var info = GoodsList[i];
                if (info != null && info.TheGood != null)
                {
                    if (Utils.EqualNoCase(info.TheGood.FileName, fileName))
                    {
                        return info;
                    }
                }
            }
            return null;
        }

        public static int GetGoodsNum(string fileName)
        {
            switch (Type)
            {
                case ListType.TypeByGoodType:
                {
                    var info = GetGoodsItemInfoFromFileName(fileName);
                    if (info != null)
                    {
                        return info.Count;
                    }

                    return 0;
                }
                case ListType.TypeByGoodItem:
                {
                    var count = 0;
                    for (var i = ListIndexBegin; i <= ListIndexEnd; i++)
                    {
                        var info = GoodsList[i];
                        if (info != null && info.TheGood != null)
                        {
                            if (Utils.EqualNoCase(info.TheGood.FileName, fileName))
                            {
                                count++;
                            }
                        }
                    }

                    return count;
                }
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        public static void DeleteGood(string fileName)
        {
            var i = ListIndexBegin;
            for (; i <= ListIndexEnd; i++)
            {
                if (GoodsList[i] != null &&
                    GoodsList[i].TheGood != null &&
                    Utils.EqualNoCase(GoodsList[i].TheGood.FileName, fileName))
                    break;
            }
            if (i <= ListIndexEnd)
            {
                var info = GoodsList[i];
                var good = info.TheGood;
                if (info.Count == 1)
                    GoodsList[i] = null;
                else
                    info.Count -= 1;

                //if goods is unequiped
                if (i >= EquipIndexBegin && i <= EquipIndexEnd && GoodsList[i] == null)
                {
                    if (Globals.ThePlayer != null)
                        Globals.ThePlayer.UnEquiping(good);
                }
            }
        }

        // public static bool CanEquip(int goodIndex, Good.EquipPosition position)
        // {
        //     return (!IsInEquipRange(goodIndex) &&
        //         Good.CanEquip(Get(goodIndex), position));
        // }

        public static bool CanEquip(int goodIndex, Good.EquipPosition position, Character player = null)
        {
            if (player == null)
            {
                return (!IsInEquipRange(goodIndex) && Good.CanEquipToPosition(Get(goodIndex), position));
            }
            else
            {
                return (!IsInEquipRange(goodIndex) && Good.CanEquip(Get(goodIndex), player, position));
            }
        }

        public static void ChangePlayerEquiping(int index1, int index2)
        {
            Good equip = null;
            Good currentEquip = null;
            
            if (GuiManager.EquipInterface.Index == Globals.PlayerIndex)
            {
                if (IsInEquipRange(index1))
                {
                    equip = Get(index1);
                    currentEquip = Get(index2);
                }
                else if (IsInEquipRange(index2))
                {
                    equip = Get(index2);
                    currentEquip = Get(index1);
                }
                Globals.ThePlayer.Equiping(equip, currentEquip);
            }
            else
            {
                if (IsInPartnerEquipRange(index1))
                {
                    equip = Get(index1);
                    currentEquip = Get(index2);
                }
                else if (IsInPartnerEquipRange(index2))
                {
                    equip = Get(index2);
                    currentEquip = Get(index1);
                }
                int partnerIndex = GuiManager.EquipInterface.Index;
                Partner partner = NpcManager.GetPartnerByName(PartnerList.GetName(partnerIndex));
                if (partner != null) { partner.Equiping(equip, currentEquip); }
            }
        }

        public static bool PlayerUnEquiping(int equipIndex, out int newIndex)
        {
            if (GuiManager.EquipInterface.Index == Globals.PlayerIndex)
            {
                if (IsInEquipRange(equipIndex))
                {
                    if (MoveEquipItemToList(equipIndex, out newIndex))
                    {
                        Globals.ThePlayer.UnEquiping(Get(newIndex));
                        return true;
                    }
                }
            }
            else
            {
                if (IsInPartnerEquipRange(equipIndex))
                {
                    int partnerIndex = GuiManager.EquipInterface.Index;
                    Partner partner = NpcManager.GetPartnerByName(PartnerList.GetName(partnerIndex));
                    if (partner != null && MoveEquipItemToList(equipIndex, out newIndex))
                    {
                        partner.UnEquiping(Get(newIndex));
                        return true;
                    }
                }
            }
            newIndex = 0;
            return false;
        }

        public static bool DeleteGood(string fileName, int amount)
        {
            if (amount <= 0) return false;
            switch (Type)
            {
                case ListType.TypeByGoodType:
                {
                    for (var i = ListIndexBegin; i <= ListIndexEnd; i++)
                    {
                        var info = GoodsList[i];
                        if (info != null && info.TheGood != null)
                        {
                            if (Utils.EqualNoCase(info.TheGood.FileName, fileName))
                            {
                                if (info.Count < amount)
                                {
                                    return false;
                                }
                                info.Count -= amount;
                                if (info.Count == 0)
                                {
                                    GoodsList[i] = null;
                                }
                                GuiManager.UpdateGoodItemView(i);
                                return true;
                            }
                        }
                    }
                }
                    break;
                case ListType.TypeByGoodItem:
                {
                    var indexToDelete = new List<int>();
                    for (var i = ListIndexBegin; i <= ListIndexEnd; i++)
                    {
                        var info = GoodsList[i];
                        if (info != null && info.TheGood != null)
                        {
                            if (Utils.EqualNoCase(info.TheGood.FileName, fileName))
                            {
                                indexToDelete.Add(i);
                                amount--;
                                if (amount == 0)
                                {
                                    break;
                                }
                            }
                        }
                    }

                    if (amount == 0)
                    {
                        foreach (var i in indexToDelete)
                        {
                            GoodsList[i] = null;
                            GuiManager.UpdateGoodItemView(i);
                        }
                        return true;
                    }
                }
                    break;
                default:
                    Log.LogMessage(@"GoodsListType 设置错误，请检查Content\ui\UI_Settings.ini文件");
                    return false;
            }
            return false;
        }

        public static void UsingGood(int goodIndex)
        {
            if (IsInEquipRange(goodIndex))
            {
                //Can't use equiped good.
                return;
            }

            var info = GetItemInfo(goodIndex);
            if (info == null) return;
            var good = info.TheGood;
            if (good.User != null && good.User.Length > 0)
            {
                if (!good.User.Contains(Globals.ThePlayer.Name))
                {
                    //Current player can't use this good
                    GuiManager.ShowMessage("使用者：" + string.Join("，", good.User));
                    return;
                }
            }
            if (good.MinUserLevel > 0 && Globals.ThePlayer.Level < good.MinUserLevel)
            {
                GuiManager.ShowMessage("需要等级" + good.MinUserLevel);
                return;
            }
            if (good != null)
            {
                switch (good.Kind)
                {
                    case Good.GoodKind.Drug:
                        {
                            if (IsInBottomGoodsRange(goodIndex))
                            {
                                if (info.RemainColdMilliseconds > 0)
                                {
                                    GuiManager.ShowMessage("该物品尚未冷却");
                                    return;
                                }

                                if (info.TheGood.ColdMilliSeconds > 0)
                                {
                                    info.RemainColdMilliseconds = info.TheGood.ColdMilliSeconds;
                                }
                            }

                            if (Globals.ThePlayer.UseDrug(good,true))
                            {

                                if (info.Count == 1)
                                    GoodsList[goodIndex] = null;
                                else
                                    info.Count -= 1;
                            }
                            var sound = Utils.GetSoundEffect("界-使用物品.wav");
                            if (sound != null)
                            {
                                sound.Play();
                            }
                        }
                        break;
                    case Good.GoodKind.Equipment:
                        {
                            Character user = Globals.ThePlayer;
                            if (GuiManager.EquipInterface.IsShow && GuiManager.EquipInterface.Index != Globals.PlayerIndex)
                            {
                                user = NpcManager.GetPartnerByName(PartnerList.GetName(GuiManager.EquipInterface.Index));
                            }
                            if (Good.CanEquipToUser(good, user))
                            {
                                GuiManager.EquipInterface.EquipGood(goodIndex);
                            }
                            else
                            {
                                GuiManager.ShowMessage("当前角色无法装备");
                            }
                        }
                        break;
                    case Good.GoodKind.Event:
                        {
                            ScriptManager.RunScript(Utils.GetScriptParser(
                                good.Script, null, Utils.ScriptCategory.Good), good);
                        }
                        break;
                    default:
                        throw new ArgumentOutOfRangeException();
                }

                GuiManager.UpdateGoodItemView(goodIndex);
            }
        }

        public static Good Get(int index)
        {
            var itemInfo = GetItemInfo(index);
            return (itemInfo != null) ?
                itemInfo.TheGood :
                null;
        }

        public static Texture GetTexture(int index)
        {
            var good = Get(index);
            if (good != null)
            {
                if (index >= BottomIndexBegin && index <= BottomIndexEnd)
                    return new Texture(good.Icon);
                else
                    return new Texture(good.Image);
            }
            return null;
        }

        public static Asf GetImage(int index)
        {
            var good = Get(index);
            if (good != null)
                return good.Image;
            return null;
        }

        public static Asf GetIcon(int index)
        {
            var good = Get(index);
            if (good != null)
                return good.Icon;
            return null;
        }

        public static GoodsItemInfo GetItemInfo(int index)
        {
            return IndexInRange(index) ? GoodsList[index] : null;
        }

        public static void SetItemInfo(int index,GoodsItemInfo itemInfo)//扩展几个位置存队内伙伴的装备信息
        {
            if (IndexInRange(index))
            {
                GoodsList[index] = itemInfo;
            }
        }

        public static void SavePartnerEquipToFile(string partnerGoodsFilePath, Dictionary<int, Good> equips)//将队友更换的装备保存至相应文件
        {
            var parser = new FileIniDataParser();
            var data = parser.ReadFile(Globals.GetRightpath(partnerGoodsFilePath), Globals.LocalEncoding);
            if (data != null)
            {
                var headSection = data.Sections["Head"];
                int headCount = 0;
                int.TryParse(headSection["Count"], out headCount);
                foreach (var equip in equips)
                {
                    int equipIndex = equip.Key + EquipIndexBegin - 1;
                    string strIndex = equipIndex.ToString();
                    if (data.Sections.ContainsSection(strIndex))
                    {
                        var section = data[strIndex];
                        section.RemoveAllKeys();
                        section.AddKey("IniFile", equip.Value.FileName);
                        section.AddKey("Number", "1");
                    }
                    else
                    {
                        data.Sections.AddSection(strIndex);
                        var section = data[strIndex];
                        section.AddKey("IniFile", equip.Value.FileName);
                        section.AddKey("Number", "1");
                        headCount++;
                    }
                }
                headSection.RemoveKey("Count");
                headSection.AddKey("Count", headCount.ToString());
            }
            //是否要新建？
            File.WriteAllText(Globals.GetRightpath(partnerGoodsFilePath), data.ToString(), Globals.LocalEncoding);
        }

        public class GoodsItemInfo
        {
            public Good TheGood;
            public int Count;
            private float _remainColdMilliseconds;
            public float RemainColdMilliseconds
            {
                get { return _remainColdMilliseconds; }
                set
                {
                    if (value < 0)
                    {
                        value = 0;
                    }
                    _remainColdMilliseconds = value;
                }
            }

            public GoodsItemInfo(string fileName, int count)
            {
                var good = Utils.GetGood(fileName);
                if (good != null) TheGood = good;
                Count = count;
            }

            public GoodsItemInfo(Good good, int count)
            {
                TheGood = good;
                Count = count;
            }
        }
    }
}