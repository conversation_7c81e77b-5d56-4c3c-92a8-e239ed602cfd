﻿using Engine.Gui.Base;
using Engine.Script;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input;
using System;
using System.Collections.Generic;
using System.Reflection.Metadata;
using System.Runtime.CompilerServices;
using Texture = Engine.Gui.Base.Texture;

namespace Engine.Gui
{
    public sealed class SystemGui : GuiItem
    {
        public int CurrentIndex { get; set; } = 0;
        private GuiItem _saveloadButton;
        private GuiItem _opetionButton;
        private GuiItem _exitButton;
        private GuiItem _returnButton;

        private IList<GuiItem> _buttons = new List<GuiItem>();

        private int _leftAdjust;
        private int _topAdjust;

        public SystemGui()
        {
            var cfg = GuiManager.Setttings.Sections["System"];
            BaseTexture = new Texture(Utils.GetAsf(null, cfg["Image"]));
            Width = BaseTexture.Width;
            Height = BaseTexture.Height;
            _leftAdjust = int.Parse(cfg["LeftAdjust"]);
            _topAdjust = int.Parse(cfg["TopAdjust"]);
            RePosition();

            //Button
            cfg = GuiManager.Setttings.Sections["System_SaveLoad_Btn"];
            var asf = Utils.GetAsf(@"asf\ui\system", "saveload.asf");
            var clickedSound = Utils.GetSoundEffect(cfg["Sound"]);
            _saveloadButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                new Texture(asf, 0, 1),
                null,
                new Texture(asf, 1, 1),
                null,
                clickedSound);

            cfg = GuiManager.Setttings.Sections["System_Option_Btn"];
            asf = Utils.GetAsf(@"asf\ui\system", "option.asf");
            clickedSound = Utils.GetSoundEffect(cfg["Sound"]);
            _opetionButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                new Texture(asf, 0, 1),
                null,
                new Texture(asf, 1, 1),
                null,
                clickedSound);

            cfg = GuiManager.Setttings.Sections["System_Exit_Btn"];
            asf = Utils.GetAsf(@"asf\ui\system", "quit.asf");
            clickedSound = Utils.GetSoundEffect(cfg["Sound"]);
            _exitButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                new Texture(asf, 0, 1),
                null,
                new Texture(asf, 1, 1),
                null,
                clickedSound);

            cfg = GuiManager.Setttings.Sections["System_Return_Btn"];
            asf = Utils.GetAsf(@"asf\ui\system", "return.asf");
            clickedSound = Utils.GetSoundEffect(cfg["Sound"]);
            _returnButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                new Texture(asf, 0, 1),
                null,
                new Texture(asf, 1, 1),
                null,
                clickedSound);

            _buttons.Add(_saveloadButton);
            _buttons.Add(_opetionButton);
            _buttons.Add(_exitButton);
            _buttons.Add(_returnButton);

            RegisterEvent();

            IsShow = false;
        }

        public void RePosition()
        {
            Position = new Vector2(
                (Globals.WindowWidth - Width) / 2f + _leftAdjust,
                0 + _topAdjust);
        }

        private void RegisterEvent()
        {
            _saveloadButton.Click += (arg1, arg2) => GuiManager.ShowSaveLoad();
            _opetionButton.Click += (arg1, arg2) =>
            {
                //GuiManager.ShowMessage("请用游戏设置程序进行设置");
                GuiManager.ShowOptionGui();
            };
            _exitButton.Click += (arg1, arg2) =>
            {
                IsShow = false;
                ScriptManager.RunScript(Utils.GetScriptParser("return.txt"));
            };
            _returnButton.Click += (arg1, arg2) => GuiManager.ShowSystem(false);
        }

        public override void Update(GameTime gameTime)
        {
            if (!IsShow) return;
            base.Update(gameTime);
            HandleInput();
            _saveloadButton.Update(gameTime);
            _opetionButton.Update(gameTime);
            _exitButton.Update(gameTime);
            _returnButton.Update(gameTime);
        }

        private void HandleInput()
        {
            var keyboardState = Keyboard.GetState();
            var gamepadState = GamePad.GetState(PlayerIndex.One);
            if ((keyboardState.IsKeyDown(Keys.Down) && Globals.TheGame.LastKeyboardState.IsKeyUp(Keys.Down))
                || (gamepadState.IsButtonDown(Buttons.DPadDown) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.DPadDown)))
            {
                CurrentIndex = (CurrentIndex + 1) % _buttons.Count;
                HanldeButtonDisplay();
            }
            else if ((keyboardState.IsKeyDown(Keys.Up) && Globals.TheGame.LastKeyboardState.IsKeyUp(Keys.Up))
                || (gamepadState.IsButtonDown(Buttons.DPadUp) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.DPadUp)))
            {
                CurrentIndex = (CurrentIndex - 1 + _buttons.Count) % _buttons.Count;
                //_guiItems[CurrentIndex].IsShow = false
                HanldeButtonDisplay();
            }
            if ((keyboardState.IsKeyDown(Keys.Enter) && Globals.TheGame.LastKeyboardState.IsKeyUp(Keys.Enter))
                || (gamepadState.IsButtonDown(Buttons.A) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.A)))
            {
                if (_buttons[CurrentIndex].IsFocus)
                {
                    //触发点击事件
                    _buttons[CurrentIndex].InvokeClick(null, null);
                }
            }
        }

        private void HanldeButtonDisplay()
        {
            for (var i = 0; i < _buttons.Count; i++)
            {
                if (i == CurrentIndex)
                {
                    _buttons[i].IsFocus = true;
                }
                else
                {
                    _buttons[i].IsFocus = false;
                }
            }
        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;
            base.Draw(spriteBatch);
            _saveloadButton.Draw(spriteBatch);
            _opetionButton.Draw(spriteBatch);
            _exitButton.Draw(spriteBatch);
            _returnButton.Draw(spriteBatch);
        }
    }
}