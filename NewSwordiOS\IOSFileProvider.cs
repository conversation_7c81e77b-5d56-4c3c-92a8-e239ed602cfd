using Engine;
using Foundation;
using System;
using System.IO;
using System.Text;

namespace NewSwordiOS
{
    public class IOSFileProvider : IFileProvider
    {
        //资源文件有两种来源：
        //一种是iOS应用包内的资源文件，通过File类读取。只读，不可写。
        //另一种是可写目录下的文件，主要是ini和save等文本数据文件，通过File类来读取和写入。

        private readonly string _basePath; // 可写目录的路径
        private readonly string _bundlePath; // 应用包内资源路径

        public IOSFileProvider(string basePath)
        {
            _basePath = basePath; // 例如 Environment.GetFolderPath(Environment.SpecialFolder.Personal)
            _bundlePath = NSBundle.MainBundle.BundlePath;
        }

        /// <summary>
        /// 获取可写目录中的完整路径
        /// </summary>
        private string GetFullPath(string path) => Path.Combine(_basePath, path);

        /// <summary>
        /// 获取应用包内的完整路径
        /// </summary>
        private string GetBundlePath(string path) => Path.Combine(_bundlePath, path);

        /// <summary>
        /// 获取映射前的路径
        /// </summary>
        public string GetRealPath(string path)
        {
            return path; // iOS不需要路径映射
        }

        /// <summary>
        /// 处理iOS平台路径
        /// </summary>
        public string HandlePlatformPath(string path)
        {
            // 统一路径分隔符和大小写
            path = path.Replace('\\', '/');
            //if (!path.EndsWith(".txt") && !path.StartsWith("script"))
            if (!path.StartsWith(_basePath, StringComparison.OrdinalIgnoreCase)
                && !path.StartsWith(_bundlePath, StringComparison.OrdinalIgnoreCase))
            {
                path = path.ToLowerInvariant(); // 全部转成小写，确保跨平台一致性
            }

            // 如果是ini或save开头的路径，确保它们指向可写目录
            if ((path.StartsWith("ini") || path.StartsWith("save")) && !path.Contains(_basePath))
            {
                path = Path.Combine(_basePath, path);
            }

            return path;
        }

        public bool Exists(string path)
        {
            //Console.WriteLine($"Check file existence: {path}"); //debug 调试输出
            path = HandlePlatformPath(path);

            // 如果是ini或save开头的路径，检查可写目录
            if (path.Contains(_basePath))
            {
                return File.Exists(path);
            }

            // 否则检查应用包内
            var bundlePath = GetBundlePath(path);
            var isExists = File.Exists(bundlePath);
            if (!isExists)
            {
                Console.WriteLine($"File NOT exists in bundle: {bundlePath}"); // 调试输出 
            }
            return isExists;
        }

        public string ReadAllText(string path)
        {
            path = HandlePlatformPath(path);

            // 如果是ini或save开头的路径，从可写目录读取
            if (path.Contains(_basePath))
            {
                return File.ReadAllText(path);
            }

            // 否则从应用包内读取
            return File.ReadAllText(GetBundlePath(path));
        }

        public void WriteAllText(string path, string contents, Encoding encoding)
        {
            path = HandlePlatformPath(path);

            // 写入操作只能在可写目录进行
            if (!path.Contains(_basePath))
            {
                path = GetFullPath(path);
            }

            File.WriteAllText(path, contents ?? string.Empty, encoding);
        }

        public Stream OpenRead(string path)
        {
            path = HandlePlatformPath(path);

            // 如果是ini或save开头的路径，从可写目录读取
            if (path.Contains(_basePath))
            {
                return File.OpenRead(path);
            }

            // 否则从应用包内读取
            return File.OpenRead(GetBundlePath(path));
        }

        public byte[] ReadAllBytes(string path)
        {
            path = HandlePlatformPath(path);

            // 如果是ini或save开头的路径，从可写目录读取
            if (path.Contains(_basePath))
            {
                return File.ReadAllBytes(path);
            }

            // 否则从应用包内读取
            return File.ReadAllBytes(GetBundlePath(path));
        }

        public string[] ReadAllLines(string path, Encoding encoding)
        {
            path = HandlePlatformPath(path);

            // 如果是ini或save开头的路径，从可写目录读取
            if (path.Contains(_basePath))
            {
                return File.ReadAllLines(path, encoding);
            }

            // 否则从应用包内读取
            return File.ReadAllLines(GetBundlePath(path), encoding);
        }

        public DateTime GetLastWriteTime(string path)
        {
            path = HandlePlatformPath(path);

            // 获取文件最后修改时间只对可写目录有意义
            if (!path.Contains(_basePath))
            {
                path = GetFullPath(path);
            }

            return File.GetLastWriteTime(path);
        }

        public void Delete(string path)
        {
            path = HandlePlatformPath(path);

            // 删除操作只能在可写目录进行
            if (!path.Contains(_basePath))
            {
                path = GetFullPath(path);
            }

            if (File.Exists(path))
            {
                File.Delete(path);
            }
        }

        public FileStream CreateFile(string path)
        {
            path = HandlePlatformPath(path);

            // 创建文件只能在可写目录进行
            if (!path.Contains(_basePath))
            {
                path = GetFullPath(path);
            }

            return File.Create(path);
        }
    }
}
