﻿using Engine.Gui.Base;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Audio;
using Microsoft.Xna.Framework.Graphics;
using System;
using Texture = Engine.Gui.Base.Texture;

namespace Engine.Gui
{
    public class OptionGui : GuiItem
    {
        private CheckItem _enableTouchPad;
        private CheckItem _showDamage;

        private ScrollBar _soundEffectVolumeBar;
        private ScrollBar _scaleBar;

        private GuiItem _returnButton;

        private int _leftAdjust;
        private int _topAdjust;

        public event Action<object, EventArgs> ScaleChanged;
        public OptionGui()
        {
            var cfg = GuiManager.Setttings.Sections["System"];
            var bgImgPath = @"asf\ui\option\window-option.asf";
            BaseTexture = new Texture(Utils.GetAsf(null, bgImgPath));
            Width = BaseTexture.Width;
            Height = BaseTexture.Height;
            _leftAdjust = int.Parse(cfg["LeftAdjust"]);
            _topAdjust = int.Parse(cfg["TopAdjust"]);
            Position = new Vector2(
                (Globals.WindowWidth - Width) / 2f + _leftAdjust,
                _topAdjust);

            //Todo:暂时使用System的位置配置
            //cfg = GuiManager.Setttings.Sections["System_Return_Btn"];
            //var asf = Utils.GetAsf(@"asf\ui\option", "return.asf");
            //var clickedSound = Utils.GetSoundEffect(cfg["Sound"]);
            //_returnButton = new GuiItem(this,
            //    new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
            //    int.Parse(cfg["Width"]),
            //    int.Parse(cfg["Height"]),
            //new Texture(asf, 0, 1),
            //    null,
            //    null,
            //    null,
            //    clickedSound);

            cfg = GuiManager.Setttings.Sections["System_Return_Btn"];
            var asf = Utils.GetAsf(@"asf\ui\system", "return.asf");
            var clickedSound = Utils.GetSoundEffect(cfg["Sound"]);
            _returnButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                new Texture(asf, 0, 1),
                null,
                new Texture(asf, 1, 1),
                null,
                clickedSound);

            cfg = GuiManager.Setttings.Sections["System_Option_Btn"];
            asf = Utils.GetAsf(@"asf\ui\option", "checkbox.asf");
            _enableTouchPad = new CheckItem(this,
                new Vector2(int.Parse(cfg["Left"]) + 42, int.Parse(cfg["Top"]) - 48),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                uncheckedTexture: new Texture(asf, 1, 1),
                checkedTexture: new Texture(asf, 0, 1),
                null,
                null,
                null,
                clickedSound);

            _enableTouchPad.IsChecked = !Globals.IsTouchPadDisabled;

            //cfg = GuiManager.Setttings.Sections["System_Damage_Btn"];
            asf = Utils.GetAsf(@"asf\ui\option", "checkbox.asf");
            _showDamage = new CheckItem(this,
                new Vector2(int.Parse(cfg["Left"]) + 42, int.Parse(cfg["Top"]) + 35),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                uncheckedTexture: new Texture(asf, 1, 1),
                checkedTexture: new Texture(asf, 0, 1),
                null,
                null,
                null,
                clickedSound);

            _showDamage.IsChecked = Globals.IsShowDamage;

            _returnButton.Click += delegate
            {
                Globals.SaveAllSetting();
                IsShow = false;
                GuiManager.ShowOptionGui(false);
            };

            _enableTouchPad.Click += TouchPadEnableSwitch;

            _showDamage.Click += delegate
            {
                _showDamage.IsChecked = !_showDamage.IsChecked;
                Globals.IsShowDamage = _showDamage.IsChecked;
            };

            var slideButtonImage = @"asf\ui\option\slidebtn.asf";
            var scrollBarPosition = new Vector2(72, 300);
            var slideTexture = Utils.GetAsf(null, slideButtonImage);
            var slideBaseTexture = new Texture(slideTexture);
            var slideClikedTexture = new Texture(slideTexture, 0, 1);
            var slideButton = new GuiItem(this,
                Vector2.Zero,
                slideBaseTexture.Width,
                slideBaseTexture.Height,
                slideBaseTexture,
                null,
                slideClikedTexture,
                null,
                Utils.GetSoundEffect("界-大按钮.wav"));

            //_soundEffectVolumeBar = new ScrollBar(this,
            //    160,
            //    50,
            //    null,
            //    ScrollBar.ScrollBarType.Horizontal,
            //    slideButton,
            //    scrollBarPosition,
            //    0,
            //    10,
            //    5);
            ////_soundEffectVolumeBar.IsFocus = true;
            //_soundEffectVolumeBar.Scrolled += SoundEffectVolumeBar_Scrolled;

            int defaultScale = (int)(Globals.Scale * 2);
            _scaleBar = new ScrollBar(this,
                160,
                50,
                null,
                ScrollBar.ScrollBarType.Horizontal,
                slideButton,
                scrollBarPosition,
                2,
                6,
                defaultScale);
            _scaleBar.Scrolled += ScaleBar_Scrolled;

            IsShow = false;
        }

        private void ScaleBar_Scrolled(object arg1, ScrollBar.ScrolledEvent @event)
        {
            Globals.Scale = _scaleBar.Value / 2;

            Globals.ConfigureWindowSize();

            Globals.TheGame.ConfigureGraphics();
            //Globals.TheGame.Graphics.ApplyChanges();

            Position = new Vector2(
                (Globals.WindowWidth - Width) / 2f + _leftAdjust,
                _topAdjust);

            if (ScaleChanged != null)
                ScaleChanged(this, EventArgs.Empty);
        }

        private void SoundEffectVolumeBar_Scrolled(object arg1, ScrollBar.ScrolledEvent @event)
        {
            SoundEffect.MasterVolume = _soundEffectVolumeBar.Value / _soundEffectVolumeBar.MaxValue;
        }

        private void TouchPadEnableSwitch(object arg1, MouseLeftClickEvent @event)
        {
            _enableTouchPad.IsChecked = !_enableTouchPad.IsChecked;
            Globals.IsTouchPadDisabled = !_enableTouchPad.IsChecked;
        }

        public override void Update(GameTime gameTime)
        {
            base.Update(gameTime);
            _returnButton.Update(gameTime);
            _enableTouchPad.Update(gameTime);
            _showDamage.Update(gameTime);
            //_soundEffectVolumeBar.Update(gameTime);
            _scaleBar.Update(gameTime);
        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;
            base.Draw(spriteBatch);
            _returnButton.Draw(spriteBatch);
            _enableTouchPad.Draw(spriteBatch);
            _showDamage.Draw(spriteBatch);
            //_soundEffectVolumeBar.Draw(spriteBatch);
            _scaleBar.Draw(spriteBatch);
        }
    }
}
