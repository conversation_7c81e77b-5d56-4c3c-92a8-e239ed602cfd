﻿using System.Collections.Generic;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Audio;

namespace Engine
{
    public static class SoundManager
    {
        static private readonly Dictionary<int,SoundEffectInstance> _soundEffectInstances = new Dictionary<int, SoundEffectInstance>();
        static public void Play3DSoundOnece(SoundEffect soundEffect, Vector2 direction)
        {
            if (soundEffect == null) return;

            SoundEffectInstance instance;
            var hash = soundEffect.GetHashCode();
            if (_soundEffectInstances.ContainsKey(hash))
            {
                instance = _soundEffectInstances[hash];
            }
            else
            {
                instance = soundEffect.CreateInstance();
                instance.IsLooped = false; // 确保声音只播放一次
                _soundEffectInstances[hash] = instance;
            }
            Apply3D(instance, direction);
            instance.Play();
        }

        /// <summary>
        /// Apply 3D effect to sound effect instance.
        /// </summary>
        /// <param name="soundEffectInstance">The sound effect instance.</param>
        /// <param name="direction">The directon and distance from listenr to sound instance.</param>
        public static void Apply3D(SoundEffectInstance soundEffectInstance, Vector2 direction)
        {
            if (soundEffectInstance == null) return;

            var length = direction.Length();
            var listener = new AudioListener();
            var emitter = new AudioEmitter();
            //listener.Position = Vector3.Zero;
            //emitter.Position = Vector3.Zero;
            // 设置监听器和发射器的位置，//todo:direction已经是计算过的
            listener.Position = new Vector3(Globals.TheCarmera.CarmerRegionInWorld.Center.X, 0, Globals.TheCarmera.CarmerRegionInWorld.Center.Y); // 听者位置
            emitter.Position = new Vector3(direction.X, 0, direction.Y); // 声音源位置
            if (length >= 0 &&
                length < Globals.SoundMaxDistance)
            {
                direction.Normalize();
                var percent = length / Globals.SoundMaxDistance;
                //direction *= (percent * Globals.Sound3DMaxDistance);
                emitter.Position = listener.Position + new Vector3(direction.X, 0, direction.Y);
                // 调整音量：距离越远，音量越小
                soundEffectInstance.Volume = 1 - percent; // 音量衰减比例（最大音量为1，最小为0）
            }
            else if(length != 0)
            {
                emitter.Position = new Vector3(999999f);
                soundEffectInstance.Volume = 0;
            }
            soundEffectInstance.Apply3D(listener, emitter);
        }

        public static void PlaySoundEffectOnce(SoundEffect soundEffect)
        {
            if(soundEffect != null)
                soundEffect.Play();
        }

        public static void ClearCache()
        {
            _soundEffectInstances.Clear();
        }
    }
}
