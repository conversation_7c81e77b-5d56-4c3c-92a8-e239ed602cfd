﻿using Engine.Gui.Base;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Texture = Engine.Gui.Base.Texture;

namespace Engine.Gui
{
    public class TopGui : GuiItem
    {
        public const int MAXITEM = 4;
        private GuiItem[] _buttons = new GuiItem[MAXITEM];

        private int _leftAdjust;
        private int _topAdjust;

        public TopGui()
        {
            var cfg = GuiManager.Setttings.Sections["Top"];
            BaseTexture = new Texture(Utils.GetAsf(null, cfg["Image"]));
            Width = BaseTexture.Width;
            Height = BaseTexture.Height;
            //Position = new Vector2((Globals.WindowWidth - BaseTexture.Width) / 2f + int.Parse(cfg["LeftAdjust"]),
            //    0f + int.Parse(cfg["TopAdjust"]));  //月影
            _leftAdjust = int.Parse(cfg["LeftAdjust"]);
            _topAdjust = int.Parse(cfg["TopAdjust"]);
            RePosition();
            InitializeItems();
        }

        public void RePosition()
        {
            Position = new Vector2((Globals.WindowWidth - BaseTexture.Width) / 2f + _leftAdjust,
                (Globals.WindowHeight - BaseTexture.Height) + _topAdjust);    //新剑侠
        }

        private void RegisterClickHandler()
        {
            int i = 0;
            _buttons[i++].Click += (arg1, arg2) => GuiManager.ToggleEquipGuiShow();
            _buttons[i++].Click += (arg1, arg2) => GuiManager.ToggleGoodsGuiShow();
            _buttons[i++].Click += (arg1, arg2) => GuiManager.ToggleMemoGuiShow();
            _buttons[i++].Click += (arg1, arg2) => GuiManager.ShowSystem();
        }

        private void InitializeItems()
        {
            string[] sectionNames =
            {
                //"Top_State_Btn",
                "Top_Equip_Btn",
                //"Top_XiuLian_Btn",
                "Top_Goods_Btn",
                //"Top_Magic_Btn",
                "Top_Memo_Btn",
                "Top_System_Btn",
            };
            for (var i = 0; i < MAXITEM; i++)
            {
                var cfg = GuiManager.Setttings.Sections[sectionNames[i]];
                var asf = Utils.GetAsf(null, cfg["Image"]);
                var baseTexture = new Texture(asf, 0, 1);
                var clickedTexture = new Texture(asf, 1, 1);
                _buttons[i] = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                baseTexture,
                null,
                clickedTexture,
                null,
                Utils.GetSoundEffect(cfg["Sound"]));
            }
            RegisterClickHandler();
        }

        public override void Update(GameTime gameTime)
        {
            if (!IsShow) return;
            base.Update(gameTime);
            foreach (var button in _buttons)
            {
                button.Update(gameTime);
            }
        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;
            base.Draw(spriteBatch);
            foreach (var button in _buttons)
            {
                button.Draw(spriteBatch);
            }
        }
    }
}