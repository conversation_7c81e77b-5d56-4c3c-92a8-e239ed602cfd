﻿using Engine;
using Foundation;
using Microsoft.Xna.Framework;
using System;
using UIKit;

namespace NewSwordiOS
{
    [Register("AppDelegate")]
    internal class Program : UIApplicationDelegate
    {
        private static NewSwordGame game;

        internal static void RunGame()
        {
            game = new NewSwordGame();

            // 1. 创建 MonoGame 游戏实例

            // 2. 获取游戏内置的 UIViewController（MonoGame 框架在 iOS 上封装成了一个 VC）
            var viewController = (UIViewController)game.Services.GetService(typeof(UIViewController));

            // 3. 构造视频播放器并注入到服务容器
            var videoPlayer = new IOSVideoPlayer(viewController);
            videoPlayer.Initialize();

            game.Services.AddService(typeof(IVideoPlayer), videoPlayer);

            // 4. 显示视图控制器
            var window = new UIWindow(UIScreen.MainScreen.Bounds);
            window.RootViewController = viewController;
            window.MakeKeyAndVisible();

            game.Run();
        }

        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        static void Main(string[] args)
        {
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
            UIApplication.Main(args, null, typeof(Program));
        }

        public override void FinishedLaunching(UIApplication app)
        {
            Console.WriteLine("Main method started.");
            // 获取Documents目录作为基础路径
            var _basePath = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
            // 指定需要复制的资源目录
            var directoriesToCopy = new[] { "ini", "save" };
            // 检查并更新资源
            ResourceManager.CheckAndUpdateResources(directoriesToCopy, _basePath);

            RunGame();
        }
        public override void OnActivated(UIApplication application)
        {
            // 应用进入前台时调用
            //game?.OnActivated();
        }

        public override void DidEnterBackground(UIApplication application)
        {
            // 应用进入后台时调用
            //NewSwordGame.OnDeactivated();
        }

        public override void WillTerminate(UIApplication application)
        {
            // 应用即将终止时调用
            // 可以在这里保存游戏状态等
        }
    }
}
