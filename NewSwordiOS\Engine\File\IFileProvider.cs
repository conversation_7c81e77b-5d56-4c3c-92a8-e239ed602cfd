﻿using System;
using System.IO;
using System.Text;

namespace Engine
{

    public interface IFileProvider
    {
        // 文件是否存在
        bool Exists(string path);

        // 读取文件内容为字符串
        string ReadAllText(string path);

        // 写入文件内容，指定编码
        void WriteAllText(string path, string? contents, Encoding encoding);

        /// <summary>
        /// 打开文件作为只读流
        /// </summary>
        Stream OpenRead(string path);

        // 读取文件字节数组
        byte[] ReadAllBytes(string path);

        // 读取文件内容为字符串数组，按行读取，指定编码
        string[] ReadAllLines(string path, Encoding encoding);

        // 获取文件最后修改时间
        DateTime GetLastWriteTime(string path);

        // 删除文件
        void Delete(string path);

        // 打开文件流，用于创建新文件
        FileStream CreateFile(string path);

        string GetRealPath(string path);

        string HandlePlatformPath(string path);
    }

}