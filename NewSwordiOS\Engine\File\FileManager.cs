﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Engine
{
    public static class FileManager
    {
        private static IFileProvider _fileProvider;

        public static void Initialize(IFileProvider fileProvider)
        {
            _fileProvider = fileProvider ?? throw new ArgumentNullException(nameof(fileProvider));
        }

        public static bool Exists(string path) => _fileProvider.Exists(path);

        public static string ReadAllText(string path) => _fileProvider.ReadAllText(path);

        public static Stream OpenRead(string path) => _fileProvider.OpenRead(path);

        public static void WriteAllText(string path, string? contents, Encoding encoding) =>
        _fileProvider.WriteAllText(path, contents, encoding);

        public static byte[] ReadAllBytes(string path) => _fileProvider.ReadAllBytes(Globals.GetRightpath(path));

        public static string[] ReadAllLines(string path, Encoding encoding) =>
            _fileProvider.ReadAllLines(Globals.GetRightpath(path), encoding);

        public static DateTime GetLastWriteTime(string path) => _fileProvider.GetLastWriteTime(path);

        public static void Delete(string path) => _fileProvider.Delete(path);

        public static FileStream CreateFile(string path) => _fileProvider.CreateFile(path);

        public static string GetRealPath(string path)
        {
            return _fileProvider.GetRealPath(path);
        }

        public static string HandlePlatformPath(string path)
        {
            return _fileProvider.HandlePlatformPath(path);
        }
    }

}
