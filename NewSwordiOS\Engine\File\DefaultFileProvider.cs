﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Engine
{
    public class DefaultFileProvider : IFileProvider
    {
        public bool Exists(string path) => File.Exists(path);

        public string ReadAllText(string path) => File.ReadAllText(path);

        public void WriteAllText(string path, string? contents, Encoding encoding) =>
            File.WriteAllText(path, contents ?? string.Empty, encoding);

        public Stream OpenRead(string path) => File.OpenRead(path);

        public byte[] ReadAllBytes(string path) => File.ReadAllBytes(path);

        public string[] ReadAllLines(string path, Encoding encoding) =>
            File.ReadAllLines(path, encoding);

        public DateTime GetLastWriteTime(string path) => File.GetLastWriteTime(path);

        public void Delete(string path)
        {
            if (File.Exists(path))
            {
                File.Delete(path);
            }
        }

        public FileStream CreateFile(string path) => File.Create(path);

        public string GetRealPath(string path)
        {
            return path;
        }

        public string HandlePlatformPath(string path)
        {
            path = path.Replace('\\', Path.DirectorySeparatorChar).Replace('/', Path.DirectorySeparatorChar);
            path = path.ToLower();
            return path;
        }
    }
}
