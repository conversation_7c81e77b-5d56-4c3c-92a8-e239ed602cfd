﻿using Microsoft.Xna.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Engine
{
    public class DamageInfo
    {
        public static float MAX_DISPLAY_TIME = 900f;
        public static Color DamageColor = Color.Yellow * 0.8f;
        public static Vector2 DamageOffset = new Vector2(0,-20);

        private int _damageValue;

        public int DamageValue
        {
            get { return _damageValue; }
            set { _damageValue = value; }
        }

        private float _displayTime;

        public float DisplayTime
        {
            get { return _displayTime; }
            set { _displayTime = value; }
        }

    }
}
