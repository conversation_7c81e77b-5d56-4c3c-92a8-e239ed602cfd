﻿using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input;
using Engine;
using System;
using System.IO;

namespace NewSwordiOS
{
    public class NewSwordGame : JxqyGame
    {
        private readonly GraphicsDeviceManager _graphics;
        private readonly string _basePath;

        public NewSwordGame()
        {
            _graphics = Graphics; // 使用基类的Graphics
            Content.RootDirectory = "Content";
            IsMouseVisible = true;

            // 设置为横屏
            _graphics.SupportedOrientations = DisplayOrientation.LandscapeLeft | DisplayOrientation.LandscapeRight;
            _graphics.IsFullScreen = true;

            // 获取Documents目录作为基础路径
            _basePath = Environment.GetFolderPath(Environment.SpecialFolder.Personal);

            // 初始化文件提供者
            FileProvider = new IOSFileProvider(_basePath);
        }

        protected override void Initialize()
        {
            // 设置全局基础路径
            Globals.BasePath = FileProvider != null ? _basePath + "/" : "./";

            base.Initialize();
        }

        protected override void LoadContent()
        {
            // 使用基类的_spriteBatch
            base.LoadContent();
            Globals.UseExtVideoPlayer = true; // 使用扩展视频播放器
            base.ExternalVideoPlayer = (IVideoPlayer)Services.GetService(typeof(IVideoPlayer));
        }

        protected override void Update(GameTime gameTime)
        {
            // TODO: Add your update logic here

            base.Update(gameTime);
        }

        protected override void Draw(GameTime gameTime)
        {
            GraphicsDevice.Clear(Color.CornflowerBlue);

            // TODO: Add your drawing code here

            base.Draw(gameTime);
        }

        // 当应用进入前台时触发
        public void OnActivated()
        {
            LoadEffect();
            LoadFonts();
        }

        // 当应用进入后台时触发
        public static void OnDeactivated()
        {
            // 可以在这里处理应用进入后台时的逻辑
        }
    }
}
