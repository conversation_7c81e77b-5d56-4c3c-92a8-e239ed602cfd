﻿using System;
using System.IO;
using System.Collections.Generic;
using Engine.Gui.Base;
using Engine.Map;
using IniParser;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input;
using Color = Microsoft.Xna.Framework.Color;
using Point = Microsoft.Xna.Framework.Point;
using Rectangle = Microsoft.Xna.Framework.Rectangle;
using Texture = Engine.Gui.Base.Texture;
using Engine.Script;

namespace Engine.Gui
{
    /// <summary>
    /// 赌博游戏
    /// </summary>
    public sealed class GambleGui : GuiItem
    {
        private GuiItem _xiazhuButton;
        private GuiItem _leaveButton;
        private GuiItem _upButton;
        private GuiItem _downButton;
        private GuiItem _ditu;
        private GuiItem _yada;
        private GuiItem _yaxiao;
        private GuiItem _yaxiaoButton;
        private GuiItem _yadaButton;

        private GuiItem _player;
        private GuiItem[] _boss = new GuiItem[2]; //1,赌场老板；0,吕文才
//        private GuiItem _lvwencai;

        private GuiAnimItem[]_shaizidh=new GuiAnimItem[2]; //0，摇骰子；1，开盘
        private GuiAnimItem[] _shaizi = new GuiAnimItem[3]; //三个骰子
        //       private GuiItem _kaipan;
        //private GuiItem _closeButton;
        //private LineText _mapName;
        //private LineText _bottomTip;
        //        private LineText _messageTip;
        private LineText _wanjiaduyin;
        private LineText _xiazhu;
        private LineText _zhuangjiaduyin;
//        private int _viewBeginX;
//        private int _viewBeginY;
        private const int ViewWidth = 320;
        private const int ViewHeight = 240;
        private const int MapViewDrawBeginX = 160;
        private const int MapViewDrawBeginY = 120;
        const int Ratio = 4;
        public int xiazhu;
        private int isBig = 1; //0，压小；1，压大

        //
        public int benjin;  //本金
        public int wanjiaduyin;  //玩家赌银
        private int zhuangjiaduyin;//庄家赌银
        private int portrait;//1,赌场老板；0,吕文才
        public string gamblevar;  //存放是否赌赢的变量名
        public int iswin;   //退出时判断是否赌赢,0
        private bool isend = false; //当前局是否结束
        private String _message;
        private float _elepsedMilliseconds;
        private const float MaxShowMilliseconds = 2500;


        //        private Dictionary<string, string> _showNameDictionary;

//        int isXiazhu = 0;
        String[] _dianshu = {"一", "二", "三", "四", "五", "六","七","八","九","十","十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八" };
        
        public override bool IsShow
        {
            get
            {
                return base.IsShow;
            }
            set
            {
                base.IsShow = value;
                if (value == true)
                {
                    //播放一次摇骰子动画
                    _ditu.IsShow = false;
                    _shaizidh[1].IsShow = false;
                    _shaizidh[0].PlayState = 0;
                    _shaizidh[1].PlayState = 0;
                    _shaizidh[0].PlayCount(1);
                    xiazhu = 0;
                }
            }
        }
                
        public GambleGui()
        {
            benjin = wanjiaduyin = zhuangjiaduyin = 80;
            portrait = 0;
            var cfg = GuiManager.Setttings.Sections["Gamble"];

            BaseTexture = new Texture(Utils.GetAsf(null, cfg["Image"]));
            Width = BaseTexture.Width;
            Height = BaseTexture.Height;
            Position = new Vector2(
                (Globals.WindowWidth - Width) / 2f + int.Parse(cfg["LeftAdjust"]),
                (Globals.WindowHeight - Height) / 2f + int.Parse(cfg["TopAdjust"]));
            LoadItems();
            RegisterHadler();

            IsShow = false;
        }

        public void GetParameters(List<string> parameters)
        {
            if (parameters.Count>=3)
            {
                benjin = wanjiaduyin = zhuangjiaduyin = int.Parse(parameters[0]);
                xiazhu = 0;
                portrait = int.Parse(parameters[1]);
                _boss[portrait].IsShow = true;
                _boss[portrait ^ 1].IsShow = false;
                gamblevar = parameters[2];
             }
            else
            {
                //报错！
                Log.LogMessage("Gamble 参数错误! ");
            }
        }

        private void Xiazhu()
        {
            isend = false;
            xiazhu = int.Parse(_xiazhu.Text);
            if (xiazhu == 0)
            {
                //GuiManager.ShowDialog("  不下注，开什么开!", portrait);
                GuiManager.ShowMessage("  不下注，开什么开!");
 //               isXiazhu = 0;
                _ditu.IsShow = false;
                return;
            }
            //播放一次开盘动画
            _shaizidh[0].IsShow = false;
            _shaizidh[1].IsShow = true;
            _shaizidh[1].PlayCount(1);
            
            //计算大小
            Random a =new Random();
            int i = 0;
            int j = 1;
            int sum = 0;
            for (i = 0; i < 3; i++)
            {
                j = (a.Next() % 6) + 1;
                //显示骰子
                _shaizi[i].PlayOneFrame(j - 1);
                _shaizi[i].IsShow = true;

                sum += j;
            }
            _message = _dianshu[sum - 1];//显示的点数错误
            bool wined = true;
            if (sum>9)
            {
                _message = _message + "点大";
                if(isBig==0)
                {
                    wined = false;
                }
            }
            else
            {
                _message = _message + "点小";
                if (isBig == 1)
                {
                    wined = false;
                }
            }
            if (wined == true)
            {
                _message += ",你赢了!";
                wanjiaduyin += xiazhu;
                zhuangjiaduyin -= xiazhu;
            }
            else
            {
                _message += ",你输了!";
                wanjiaduyin -= xiazhu;
                zhuangjiaduyin += xiazhu;
            }
            _wanjiaduyin.Text = wanjiaduyin.ToString();
            _zhuangjiaduyin.Text = zhuangjiaduyin.ToString();

            isend = true;
            GuiManager.ShowMessage(_message);
            _elepsedMilliseconds = 0;

            if(zhuangjiaduyin<=0 || wanjiaduyin<=0)
            {
                Leave();
            }
        }


        private void Up()
        {
            if (wanjiaduyin > 0)
            {
                wanjiaduyin = wanjiaduyin - 1;
                xiazhu = xiazhu + 1;
                _wanjiaduyin.Text = wanjiaduyin.ToString();
                _xiazhu.Text = xiazhu.ToString();
                _ditu.IsShow = false;
            }
        }

        private void Down()
        {
            if(xiazhu>0)
            {
                wanjiaduyin = wanjiaduyin + 1;
                xiazhu = xiazhu - 1;
                _wanjiaduyin.Text = wanjiaduyin.ToString();
                _xiazhu.Text = xiazhu.ToString();
                _ditu.IsShow = false;
            }
        }
        private void Leave()
        {
            iswin = 0;
            if (wanjiaduyin + xiazhu >= benjin)
            {
                iswin = 1;
            }
            IsShow = false;
            //初始化
            _shaizidh[0].PlayState = 0;
            _shaizidh[1].PlayState = 0;
        }

        private void _yada_Click()
        {
            isBig = 1;
            _yaxiao.IsShow = false;
            _yada.IsShow = true;
        }

        private void _yaxiao_Click()
        {
            isBig = 0;
            _yaxiao.IsShow = true;
            _yada.IsShow = false;
        }
        private void RegisterHadler()
        {
            _xiazhuButton.MouseLeftDown += (arg1, arg2) => Xiazhu();
            _upButton.MouseLeftDown += (arg1, arg2) => Up();
            _downButton.MouseLeftDown += (arg1, arg2) => Down();
            _leaveButton.Click += (arg1, arg2) => Leave();
            _yadaButton.Click += (arg1, arg2)=>_yada_Click();
            _yaxiaoButton.Click += (arg1, arg2) => _yaxiao_Click();
        }

        private void LoadItems()
        {
            var cfg = GuiManager.Setttings.Sections["Gamble_Xiazhu"];
            var sound = Utils.GetSoundEffect(cfg["Sound"]);
            var asf = Utils.GetAsf(null, cfg["Image"]);
            _xiazhuButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new Texture(asf, 0, 1),
                null,
                new Texture(asf, 1, 1),
                null,
                sound);

            cfg = GuiManager.Setttings.Sections["Gamble_Leave"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _leaveButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new Texture(asf, 0, 1),
                null,
                new Texture(asf, 1, 1),
                null,
                sound);

            cfg = GuiManager.Setttings.Sections["Gamble_Up_Btn"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _upButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new Texture(asf, 0, 1),
                null,
                new Texture(asf, 1, 1),
                null,
                sound);

            cfg = GuiManager.Setttings.Sections["Gamble_Down_Btn"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _downButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new Texture(asf, 0, 1),
                null,
                new Texture(asf, 1, 1),
                null,
                sound);

            cfg = GuiManager.Setttings.Sections["Gamble_ditu"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _ditu = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new Texture(asf),
                null,
                null,
                null,
                sound);

            cfg = GuiManager.Setttings.Sections["Gamble_shaizi"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _shaizidh[0] = new GuiAnimItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new AnimeTexture(asf, true),
                null,
                null,
                null,
                sound);

            cfg = GuiManager.Setttings.Sections["Gamble_shaizi1"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _shaizi[0] = new GuiAnimItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new AnimeTexture(asf),
                null,
                null,
                null,
                sound)
            {
                IsShow = false
            };

            cfg = GuiManager.Setttings.Sections["Gamble_shaizi2"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _shaizi[1] = new GuiAnimItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new AnimeTexture(asf),
                null,
                null,
                null,
                sound)
            {
                IsShow = false
            };

            cfg = GuiManager.Setttings.Sections["Gamble_shaizi3"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _shaizi[2] = new GuiAnimItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new AnimeTexture(asf),
                null,
                null,
                null,
                sound)
            {
                IsShow = false
            };

            cfg = GuiManager.Setttings.Sections["Gamble_kaipan"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _shaizidh[1] = new GuiAnimItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new AnimeTexture(asf, true),
                null,
                null,
                null,
                sound);

            cfg = GuiManager.Setttings.Sections["Gamble_button_yada"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _yadaButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new Texture(asf, 0, 1),
                null,
                new Texture(asf, 1, 1),
                null,
                sound);

            cfg = GuiManager.Setttings.Sections["Gamble_button_yaxiao"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _yaxiaoButton = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new Texture(asf, 0, 1),
                null,
                new Texture(asf, 1, 1),
                null,
                sound);

            cfg = GuiManager.Setttings.Sections["Gamble_yada"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _yada = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new Texture(asf, 0, 1),
                null,
                new Texture(asf, 1, 1),
                null,
                sound);
            {
                IsShow = true;
            }

            cfg = GuiManager.Setttings.Sections["Gamble_yaxiao"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _yaxiao = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new Texture(asf, 0, 1),
                null,
                new Texture(asf, 1, 1),
                null,
                sound)
            {
                IsShow = false
            };

            cfg = GuiManager.Setttings.Sections["Gamble_player"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _player = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new Texture(asf),
                null,
                null,
                null,
                sound)
            {
                IsShow = true
            };

            cfg = GuiManager.Setttings.Sections["Gamble_boss"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _boss[1] = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new Texture(asf),
                null,
                null,
                null,
                sound)
            {
                IsShow = true
            };

            cfg = GuiManager.Setttings.Sections["Gamble_lvwencai"];
            asf = Utils.GetAsf(null, cfg["Image"]);
            sound = Utils.GetSoundEffect(cfg["Sound"]);
            _boss[0] = new GuiItem(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                asf.Width,
                asf.Height,
                new Texture(asf),
                null,
                null,
                null,
                sound)
            {
                IsShow = true
            };

            cfg = GuiManager.Setttings.Sections["Gamble_duyin1"];
            _wanjiaduyin = new LineText(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                (LineText.Align)int.Parse(cfg["Align"]),
                wanjiaduyin.ToString(),
                Utils.GetColor(cfg["Color"]),
                Globals.FontSize10)
            {
                IsShow = true
            };

            cfg = GuiManager.Setttings.Sections["Gamble_duyin2"];
            _zhuangjiaduyin = new LineText(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                (LineText.Align)int.Parse(cfg["Align"]),
                zhuangjiaduyin.ToString(),
                Utils.GetColor(cfg["Color"]),
                Globals.FontSize10)
            {
                IsShow = true
            };
            cfg = GuiManager.Setttings.Sections["Gamble_xiazhu"];
            _xiazhu = new LineText(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                (LineText.Align)int.Parse(cfg["Align"]),
                xiazhu.ToString(),
                Utils.GetColor(cfg["Color"]),
                Globals.FontSize10)
            {
                IsShow = true
            };
        }
        

        public override void Update(GameTime gameTime)
        {
            if (!IsShow) return;
            base.Update(gameTime);
            GuiManager.IsMouseStateEated = true;
            
            //Buttons
            _xiazhuButton.Update(gameTime);
            //_leaveButton.Update(gameTime);
            _upButton.Update(gameTime);
            _downButton.Update(gameTime);
            _leaveButton.Update(gameTime);

            _yadaButton.Update(gameTime);
            _yaxiaoButton.Update(gameTime);

//            _shaizi.Update(gameTime);
            _ditu.Update(gameTime);
            _yada.Update(gameTime);
            _yaxiao.Update(gameTime);

            _wanjiaduyin.Update(gameTime);
            _zhuangjiaduyin.Update(gameTime);
            _xiazhu.Update(gameTime);
//            _shaizi[isXiazhu].Update(gameTime);
            _shaizidh[0].Update(gameTime);
            _shaizidh[1].Update(gameTime);

            _shaizi[0].Update(gameTime);
            _shaizi[1].Update(gameTime);
            _shaizi[2].Update(gameTime);

            _player.Update(gameTime);
            _boss[portrait].Update(gameTime);

            //一局结束后停顿时间
            if (isend && _shaizidh[0].PlayState == 2 && _shaizidh[1].PlayState == 2)
            {
                _elepsedMilliseconds += (float)gameTime.ElapsedGameTime.TotalMilliseconds;
                if (_elepsedMilliseconds >= MaxShowMilliseconds)
                {
                    _shaizidh[0].PlayCount(1);
                    _shaizidh[1].IsShow = false;
                    isend = false;
                }
            }

        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;
            base.Draw(spriteBatch);
            _ditu.Draw(spriteBatch);
            _xiazhuButton.Draw(spriteBatch);
            _upButton.Draw(spriteBatch);
            _downButton.Draw(spriteBatch);
            _leaveButton.Draw(spriteBatch);

            _yadaButton.Draw(spriteBatch);
            _yaxiaoButton.Draw(spriteBatch);

            _yada.Draw(spriteBatch);
            _yaxiao.Draw(spriteBatch);

            _wanjiaduyin.Draw(spriteBatch);
            _zhuangjiaduyin.Draw(spriteBatch);
            _xiazhu.Draw(spriteBatch);

            if (isend && _shaizidh[0].PlayState == 2)
            {
                _ditu.IsShow = true;
                _shaizidh[1].IsShow = true;
                _shaizidh[0].IsShow = false;
                _shaizi[0].IsShow = true;
                _shaizi[1].IsShow = true;
                _shaizi[2].IsShow = true;
                _shaizi[0].Draw(spriteBatch);
                _shaizi[1].Draw(spriteBatch);
                _shaizi[2].Draw(spriteBatch);
                _shaizidh[1].Draw(spriteBatch);

            }
            else
            {
                _ditu.IsShow = false;
                _shaizidh[0].IsShow = true;
                _shaizidh[1].IsShow = false;
                _shaizi[0].IsShow = false;
                _shaizi[1].IsShow = false;
                _shaizi[2].IsShow = false;
                _shaizidh[0].Draw(spriteBatch);
            }
            _player.Draw(spriteBatch);
            _boss[portrait].Draw(spriteBatch);
        }
    }
}
