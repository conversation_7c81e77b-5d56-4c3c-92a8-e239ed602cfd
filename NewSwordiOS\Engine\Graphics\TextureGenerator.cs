﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;

namespace Engine
{
    public static class TextureGenerator
    {
        //sourceTexture is unchanged
        public static Texture2D MakeTransparent(Texture2D sourceTexture, float transparent)
        {
            if (sourceTexture == null) return null;
            int width = sourceTexture.Width, height = sourceTexture.Height;
            var data = new Color[width * height];
            sourceTexture.GetData(data);
            var tex = new Texture2D(sourceTexture.GraphicsDevice, width, height);
            for (var idx = 0; idx < width * height; idx++)
            {
                data[idx] *= transparent;
            }
            tex.SetData(data);
            return tex;
        }

        // 添加缓存字典，使用纹理和透明度作为键
        private static Dictionary<Tuple<Texture2D, float>, Texture2D> _transparentTextureCache = 
            new Dictionary<Tuple<Texture2D, float>, Texture2D>();

        // 修改后的方法，添加缓存支持
        public static Texture2D MakeTransparentFromTop(Texture2D sourceTexture, float opaquePercentFromBottom)
        {
            if (sourceTexture == null) return null;
            if (opaquePercentFromBottom < 0f) opaquePercentFromBottom = 0f;
            else if (opaquePercentFromBottom > 1f) opaquePercentFromBottom = 1f;
            
            // 四舍五入到两位小数，减少缓存键的数量
            opaquePercentFromBottom = (float)Math.Round(opaquePercentFromBottom, 2);
            
            // 检查缓存
            var cacheKey = new Tuple<Texture2D, float>(sourceTexture, opaquePercentFromBottom);
            if (_transparentTextureCache.ContainsKey(cacheKey))
            {
                return _transparentTextureCache[cacheKey];
            }
            
            // 创建新纹理
            int width = sourceTexture.Width, height = sourceTexture.Height;
            var data = new Color[width * height];
            sourceTexture.GetData(data);
            var tex = new Texture2D(sourceTexture.GraphicsDevice, width, height);
            var transHeight = (int)(sourceTexture.Height * (1 - opaquePercentFromBottom));
            
            // 处理透明度
            for (var w = 0; w < width; w++)
            {
                for (var h = 0; h < transHeight; h++)
                {
                    data[h*width + w] *= 0;
                }
            }
            
            tex.SetData(data);
            
            // 添加到缓存
            _transparentTextureCache[cacheKey] = tex;
            
            // 如果缓存过大，清理最旧的条目
            if (_transparentTextureCache.Count > 100) // 设置合理的缓存上限
            {
                var oldestKey = _transparentTextureCache.Keys.First();
                _transparentTextureCache[oldestKey].Dispose(); // 释放纹理资源
                _transparentTextureCache.Remove(oldestKey);
            }
            
            return tex;
        }

        // 添加清理方法，在场景切换或游戏退出时调用
        public static void ClearTextureCache()
        {
            foreach (var texture in _transparentTextureCache.Values)
            {
                texture.Dispose();
            }
            _transparentTextureCache.Clear();
        }

        public static Texture2D ToGrayScale(Texture2D texture2D)
        {
            if (texture2D == null) return null;
            int width = texture2D.Width, height = texture2D.Height;
            var total = (width) * (height);
            var data = new Color[total];
            texture2D.GetData(data);
            for (var i = 0; i < total; i++)
            {
                var arg = (byte) ((data[i].R + data[i].G + data[i].B)/3);
                data[i].R = data[i].G = data[i].B = arg;
            }
            var tex = new Texture2D(texture2D.GraphicsDevice, width, height);
            tex.SetData(data);
            return tex;
        }

        public static Texture2D GetOuterEdge(Texture2D texture2D, Color color)
        {
            if (texture2D == null) return null;
            int width = texture2D.Width, height = texture2D.Height;
            var total = (width) * (height);
            var data = new Color[total];
            texture2D.GetData(data);
            var edge = new List<int>();

            for (var i = 0; i < total; i++)
            {
                if (!IsColorTransparentForNpcObj(data[i])) continue;
                var neighbers = new int[]
                {
                    i - width,
                    i - width + 1,
                    i + 1,
                    i + width + 1,
                    i + width,
                    i + width - 1,
                    i - 1,
                    i - width - 1
                };
                foreach (var neighber in neighbers)
                {
                    if (neighber >= 0 && neighber < total)
                    {
                        if (!IsColorTransparentForNpcObj(data[neighber]))
                        {
                            edge.Add(i);
                            break;
                        }
                    }
                }
            }

            //Check top right bottom left
            var beginBottom = (height - 1)*width;
            for (var w = 0; w < width; w++)
            {
                if(!IsColorTransparentForNpcObj(data[w]))
                    edge.Add(w);
                if(!IsColorTransparentForNpcObj(data[beginBottom + w]))
                    edge.Add(beginBottom + w);
            }
            var beginLeft = 0;
            var beginRight = width - 1;
            for (var h = 0; h < height; h++)
            {
                if(!IsColorTransparentForNpcObj(data[beginLeft]))
                    edge.Add(beginLeft);
                if(!IsColorTransparentForNpcObj(data[beginRight]))
                    edge.Add(beginRight);
                beginLeft += width;
                beginRight += width;
            }

            data = new Color[total];
            foreach (var i in edge)
            {
                data[i] = color;
            }
            var tex = new Texture2D(texture2D.GraphicsDevice, width, height);
            tex.SetData(data);
            return tex;
        }

        public static bool IsColorTransparentForNpcObj(Color color)
        {
            return (color.A < 200);
        }

        private class ColorTextureInfo
        {
            public Texture2D Texture;
            public int Width;
            public int Height;

            public ColorTextureInfo(Texture2D texture, int width, int height)
            {
                Texture = texture;
                Width = width;
                Height = height;
            }
        }
        static Dictionary<Color,ColorTextureInfo> _colorTextureCache = new Dictionary<Color,ColorTextureInfo>();  
        public static Texture2D GetColorTexture(Color color, int width, int height)
        {
            if (width <= 0 || height <= 0) return null;

            if (_colorTextureCache.ContainsKey(color))
            {
                var info = _colorTextureCache[color];
                if (info.Width == width && info.Height == height)
                {
                    return info.Texture;
                }
            }

            var size = width*height;
            var data = new Color[size];
            for (var i = 0; i < size; i++)
                data[i] = color;
            var texture = new Texture2D(Globals.TheGame.GraphicsDevice, width, height);
            texture.SetData(data);

            _colorTextureCache[color] = new ColorTextureInfo(texture,width,height);

            return texture;
        }

        public static Asf[] GetSnowFlake()
        {
            var asfs = new Asf[4];
            var w = Color.White;
            var t = Color.Transparent;
            var data = new Color[3*3]
            {
                t, w, t,
                w, w, w,
                t, w, t
            };
            var texture = new Texture2D(Globals.TheGame.GraphicsDevice, 3, 3);
            texture.SetData(data);
            asfs[0] = new Asf(texture);
            data = new Color[2*2]
            {
                t, w,
                w, t
            };
            texture = new Texture2D(Globals.TheGame.GraphicsDevice, 2, 2);
            texture.SetData(data);
            asfs[1] = new Asf(texture);
            data = new Color[2*2]
            {
                w, t,
                t, w
            };
            texture = new Texture2D(Globals.TheGame.GraphicsDevice, 2, 2);
            texture.SetData(data);
            asfs[2] = new Asf(texture);
            data = new Color[1]
            {
                w
            };
            texture = new Texture2D(Globals.TheGame.GraphicsDevice, 1, 1);
            texture.SetData(data);
            asfs[3] = new Asf(texture);
            return asfs;
        }

        public static Texture2D GetRaniDrop()
        {
            var w = Color.Gray*0.3f;
            var l = Color.Gray * 0.2f;
            var data = new Color[1*20]
            {
                l,
                l,
                l,
                l,
                l,
                l,
                w,
                w,
                w,
                w,
                w,
                w,
                w,
                w,
                w,
                l,
                l,
                l,
                l,
                l
            };
            var texture = new Texture2D(Globals.TheGame.GraphicsDevice, 1, 20);
            texture.SetData(data);
            return texture;
        }
    }
}