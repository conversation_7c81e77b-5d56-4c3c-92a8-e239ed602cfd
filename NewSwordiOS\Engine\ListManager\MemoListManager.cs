﻿using System;
using System.Collections.Generic;
using System.IO;
using Engine.Gui;
using IniParser;
using IniParser.Model;

namespace Engine.ListManager
{
    public static class MemoListManager
    {
        private static readonly LinkedList<string> MemoList = new LinkedList<string>(); 
        public static void LoadList(string filePath)
        {
            RenewList();
            GuiManager.UpdateMemoView();
            try
            {
                var parser = new FileIniDataParser();
                var data = parser.ReadFile(Globals.GetRightpath(filePath), Globals.LocalEncoding);
                var section = data["Memo"];
                var count = int.Parse(section["Count"]);
                for (var i = 0; i < count; i++)
                {
                    MemoList.AddLast(section[i.ToString()]);
                }
            }
            catch (Exception exception)
            {
                Log.LogFileLoadError("Memo file", filePath, exception);
            }
            GuiManager.UpdateMemoView();
        }

        public static void SaveList(string filePath)
        {
            try
            {
                var data = new IniData();
                data.Sections.AddSection("Memo");
                var memoSection = data["Memo"];
                var count = MemoList.Count;
                memoSection.AddKey("Count", count.ToString());
                var i = 0;
                foreach (var memo in MemoList)
                {
                    memoSection.AddKey(i.ToString(), memo);
                    i++;
                }
                //Write to file
                string fullPath = Globals.GetRightpath(filePath);
                try
                {
                    // 确保目录结构存在
                    string directoryPath = Path.GetDirectoryName(fullPath);
                    if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
                    {
                        Directory.CreateDirectory(directoryPath);
                        Log.LogMessage("Created directory: " + directoryPath);
                    }

                    File.WriteAllText(fullPath, data.ToString(), Globals.LocalEncoding);
                }
                catch (Exception ex)
                {
                    // 记录错误信息以便调试
                    Log.LogMessage("SaveMemoList Error: " + ex.Message);
                    Log.LogMessage("Path: " + fullPath);
                    Log.LogMessage("StackTrace: " + ex.StackTrace);
                }
            }
            catch (Exception exception)
            {
                Log.LogFileSaveError("Memo file", filePath, exception);
            }
        }

        public static void RenewList()
        {
            MemoList.Clear();
        }

        public static int GetCount()
        {
            return MemoList.Count;
        }

        public static bool IndexInRange(int index)
        {
            return (index >= 0 && index < GetCount());
        }

        public static string GetString(int index)
        {
            if (IndexInRange(index))
            {
                var node = MemoList.First;
                for (var i = 0; i < index; i++)
                {
                    node = node.Next;
                }
                return node.Value;
            }
            return "";
        }

        public static void AddMemo(string text)
        {
            text = "●" + text;
            var lines = Utils.SpliteStringInCharCount(text, 10);
            var count = lines.Count;
            //Add reversely
            for (var i = count - 1; i >= 0; i--)
            {
                MemoList.AddFirst(lines[i]);
            }
        }
    }
}