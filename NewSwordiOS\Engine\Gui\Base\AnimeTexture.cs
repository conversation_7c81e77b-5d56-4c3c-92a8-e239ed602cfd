﻿using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Engine.Gui.Base
{
    public class AnimeTexture : Texture
    {
        private int _count = 0; //循环播放次数
        private int _playcount = 0;
        private bool _isplayonce = false; //false,循环播；true，按次数播放
        private bool _isplayend = false;
        private int _playstate = 0; //0,初始;1正在播;2播放完
        private bool _isoneframe = false;

        public void PlayCount(int count)
        {
            _count = count;
            _playcount = 0;
            _isplayend = false;
            _playstate = 0;
            _isplayonce = true;
            //           _frameBegin = 0;
            //           _frameEnd = _texture.FrameCounts;
            CurrentFrameIndex = 0;
        }

        public void PlayOneFrame(int frame)
        {
            _isoneframe = true;
            _frameBegin = frame;
            _frameEnd = frame;
        }

        public bool isPlayEnd
        {
            get
            {
                return _isplayend;
            }
        }

        public int PlayState
        {
            get
            {
                return _playstate;
            }
            set
            {
                _playstate = value;
                if (_playstate == 0)
                {
                    _playcount = 0;
                    _isplayend = false;
                    _isplayonce = true;
                    CurrentFrameIndex = 0;
                }
            }
        }

        public override int CurrentFrameIndex
        {
            get
            {
                if (_isoneframe == true)
                {
                    _currentFrameIndex = _frameBegin;
                }
                return _currentFrameIndex;
            }
            set
            {
                if (_texture != null && _texture.FrameCounts > 1)
                {
                    if (_isplayonce == false)
                    {
                        if (_frameBegin == _frameEnd)
                            _currentFrameIndex = value % _texture.FrameCounts;
                        else
                        {
                            if (value >= _frameEnd)
                                _currentFrameIndex = _frameBegin;
                            else if (value < _frameBegin)
                                _currentFrameIndex = _frameBegin;
                            else
                                _currentFrameIndex = value;
                        }
                        _playstate = 1;
                    }
                    else //控制播放次数
                    {
                        if (_playcount < _count)
                        {

                            if (value >= _frameEnd)
                            {
                                _playcount++;
                                if (_playcount < _count)
                                {
                                    _currentFrameIndex = _frameBegin;
                                }
                                else
                                {
                                    _currentFrameIndex = _frameEnd;
                                }
                            }
                            else if (value < _frameBegin)
                            {
                                _currentFrameIndex = _frameBegin;
                            }
                            else
                            {
                                _currentFrameIndex = value;
                            }
                            _playstate = 1;
                        }
                        else
                        {
                            _currentFrameIndex = _texture.FrameCounts - 1;
                            _isplayend = true;
                            _playstate = 2;
                        }
                    }
                }
                else
                {
                    _currentFrameIndex = 0;
                }
            }
        }

        public AnimeTexture() { }

        public AnimeTexture(Asf asf)
        {
            _texture = asf;
        }

        public AnimeTexture(Asf asf, int frameBegin, int count)
        {
            _texture = asf;
            _frameBegin = frameBegin;
            _frameEnd = frameBegin + count;
        }

        /// <summary>
        /// 播放次数
        /// </summary>
        /// <param name="asf">asf对象</param>
        /// <param name="count">播放次数</param>
        public AnimeTexture(Asf asf, bool playonce) : base()
        {
            _texture = asf;
            _count = 1;
            _isplayonce = true;
            _frameBegin = 0;
            _frameEnd = _texture.FrameCounts;
        }


        public void Draw(SpriteBatch spriteBatch, Vector2 position, float layerDepth)
        {
            if (_texture != null)
            {
                var texture = _texture.GetFrame(CurrentFrameIndex);
                if (texture == null) return;
                //spriteBatch.Draw(
                //    texture,
                //    position,
                //    Color.White
                //    );
                Rectangle des = new Rectangle((int)position.X, (int)position.Y, texture.Width, texture.Height);
                spriteBatch.Draw(texture,
                des,
                null,
                Color.White,
                0,
                new Vector2(0),
                SpriteEffects.None,
                layerDepth);
            }
        }
    }
}
