﻿using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input.Touch;
using Microsoft.Xna.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Engine.Gui;
using Engine.Gui.Base;
using Engine.ListManager;

namespace Engine
{
    public class VirtualGamePad
    {
        public bool IsVisible { get; set; } = false;
        public Vector2 JoystickThumbPosition { get => _joystickThumbPosition; set => _joystickThumbPosition = value; }
        public Vector2 JoystickCenter { get => _joystickCenter; set => _joystickCenter = value; }
        public Vector2 JoystickDirection { get => JoystickThumbPosition - JoystickCenter; }

        private readonly Texture2D _joystickBackground;
        private readonly Texture2D _joystickThumb;
        private Vector2 _joystickCenter;
        private Vector2 _joystickThumbPosition;
        private float _joystickRadius = 110f;
        private float _joystickThumbRadius = 38f;

        private float _buttonArrangementRadius = 110f; // 辅助按钮的排列半径
        private float _buttonRadius = 35f; //一般按钮的半径

        private readonly Gui.Base.Texture attackTexture;
        private readonly Gui.Base.Texture talkTexture;
        private TouchPadButton attackButton;

        //private readonly Vector2[] _buttonPositions; // ABXY 按钮位置

        //包含的所有按钮TouchPadButton
        private readonly List<TouchPadButton> _buttons = new List<TouchPadButton>();

        private int _presingButtonIndex = -1;

        //private float _buttonRadius;

        private Matrix _inverseMatrix; //矩阵的逆，用于将屏幕坐标转换为逻辑坐标

        public void UpdateTransformMatrix(Matrix transformMatrix)
        {
            _inverseMatrix = Matrix.Invert(transformMatrix);
        }

        private Vector2 ScreenToLogical(Vector2 screenPosition)
        {
            return Vector2.Transform(screenPosition, _inverseMatrix);
        }

        public VirtualGamePad(GraphicsDevice graphicsDevice)
        {
            // 加载虚拟摇杆和按钮纹理
            _joystickBackground = CreateCircleTexture(graphicsDevice, (int)_joystickRadius, Color.Gray * 0.5f, borderColor: Color.Gray * 0.9f, borderWidth: 2);
            _joystickThumb = CreateCircleTexture(graphicsDevice, (int)_joystickThumbRadius, Color.White * 0.8f, Color.White * 0.5f, 6);
            var buttonTexture = CreateCircleTexture(graphicsDevice, (int)_buttonRadius, fillColor: Color.Gray * 0.5f, borderColor: Color.Gray * 0.9f, borderWidth: 3);

            // ABXY 按钮布局
            var screenWidth = Globals.WindowWidth;//graphicsDevice.Viewport.Width / Globals.Scale;
            var screenHeight = Globals.WindowHeight;//graphicsDevice.Viewport.Height / Globals.Scale;

            // 摇杆中心和半径
            JoystickCenter = new Vector2(150, screenHeight - 155);
            //_joystickRadius = 100;
            // 摇杆初始位置
            JoystickThumbPosition = JoystickCenter;

            // 计算主要按钮的位置（右下角）
            var _primaryButtonPosition = new Vector2(screenWidth - _buttonRadius * 3f, screenHeight - _buttonRadius * 3f);

            var iconFilePath = @"asf/ui/common/attack.png";
            attackTexture = new Gui.Base.Texture(new Asf(Utils.LoadTexture2DFromFileManager(iconFilePath)));

            iconFilePath = @"asf/ui/common/talk.png";
            talkTexture = new Gui.Base.Texture(new Asf(Utils.LoadTexture2DFromFileManager(iconFilePath)));

            // 攻击按钮
            attackButton = new TouchPadButton();
            attackButton.ButtonIndex = 0;
            attackButton.Position = _primaryButtonPosition;//new Vector2(screenWidth - 100, screenHeight - 175);
            attackButton.ButtonTexture = CreateCircleTexture(graphicsDevice, (int)(_buttonRadius * 1.6f), fillColor: Color.Gray * 0.5f, borderColor: Color.Gray * 0.9f, borderWidth: 3);
            attackButton.BaseTexture = talkTexture;
            attackButton.ButtonRadius = _buttonRadius * 1.6f;
            _buttons.Add(attackButton);

            //float angleStep = MathF.PI / 8; // 每个按钮之间的角度（90°扇形）
            float startAngle = - MathF.PI / 16; // 9点钟位置的角度
            float endAngle = MathF.PI / 2 + MathF.PI / 16; // 12点钟位置的角度
            float angleStep = (endAngle - startAngle) / 3; // 每个按钮之间的角度

            for (int i = 0; i < 4; i++)
            {
                float angle = startAngle + i * angleStep; // 从垂直向上开始排列
                var touchButton = new TouchPadButton();
                touchButton.ButtonIndex = i + 1; // 基于主要按钮的索引计算辅助按钮的索引
                touchButton.ButtonTexture = buttonTexture;
                touchButton.ButtonRadius = _buttonRadius;
                touchButton.Position = new Vector2(
                    _primaryButtonPosition.X - _buttonArrangementRadius * MathF.Cos(angle),
                    _primaryButtonPosition.Y - _buttonArrangementRadius * MathF.Sin(angle)
                );
                _buttons.Add(touchButton);
            }

            iconFilePath = @"asf/ui/common/take.png";
            buttonTexture = CreateCircleTexture(graphicsDevice, (int)(_buttonRadius * 0.8f) , fillColor: Color.Gray * 0.5f, borderColor: Color.Gray * 0.9f, borderWidth: 3);
            float angel =  MathF.PI / 2 - MathF.PI * 3 / 64;
            var interactButton = new TouchPadButton();
            interactButton.ButtonIndex = (int)TouchButtonIndex.Interactive;
            interactButton.Position = new Vector2(
                _primaryButtonPosition.X - _buttonArrangementRadius * 1.45f * MathF.Cos(angel),
                _primaryButtonPosition.Y - _buttonArrangementRadius * 1.45f * MathF.Sin(angel));
            interactButton.ButtonTexture = buttonTexture;
            interactButton.BaseTexture = new Gui.Base.Texture(new Asf(Utils.LoadTexture2DFromFileManager(iconFilePath)));
            interactButton.ButtonRadius = _buttonRadius * 0.8f;
            _buttons.Add(interactButton);

            iconFilePath = @"asf/ui/common/sit.png";
            angel = MathF.PI * 3 / 64;
            var sitButton = new TouchPadButton();
            sitButton.ButtonIndex = (int)TouchButtonIndex.Sit;
            sitButton.Position = new Vector2(
                _primaryButtonPosition.X - _buttonArrangementRadius * 1.45f * MathF.Cos(angel),
                _primaryButtonPosition.Y - _buttonArrangementRadius * 1.45f * MathF.Sin(angel));
            sitButton.ButtonTexture = buttonTexture;
            sitButton.ButtonRadius = _buttonRadius * 0.8f;
            sitButton.BaseTexture = new Gui.Base.Texture(new Asf(Utils.LoadTexture2DFromFileManager(iconFilePath)));
            _buttons.Add(sitButton);

            foreach (var button in _buttons)
            {
                button.Click += OnButtonClicked;
            }
        }

        public void RePosition()
        {
            // 计算屏幕宽和高
            var screenWidth = Globals.WindowWidth;//graphicsDevice.Viewport.Width / Globals.Scale;
            var screenHeight = Globals.WindowHeight;//graphicsDevice.Viewport.Height / Globals.Scale;

            // 摇杆中心和半径
            JoystickCenter = new Vector2(150, screenHeight - 150);
            // 摇杆初始位置
            JoystickThumbPosition = JoystickCenter;

            // 计算主要按钮的位置（右下角）
            var _primaryButtonPosition = new Vector2(screenWidth - _buttonRadius * 3f, screenHeight - _buttonRadius * 3f);

            attackButton.Position = _primaryButtonPosition;
            float startAngle = -MathF.PI / 16; // 9点钟位置的角度
            float endAngle = MathF.PI / 2 + MathF.PI / 16; // 12点钟位置的角度
            float angleStep = (endAngle - startAngle) / 3; // 每个按钮之间的角度

            for (int i = 0; i < 4; i++)
            {
                var touchButton = _buttons.First(x=>x.ButtonIndex == i+1);
                if (touchButton != null)
                {
                    float angle = startAngle + i * angleStep; // 从垂直向上开始排列
                    touchButton.Position = new Vector2(
                        _primaryButtonPosition.X - _buttonArrangementRadius * MathF.Cos(angle),
                        _primaryButtonPosition.Y - _buttonArrangementRadius * MathF.Sin(angle)
                    ); 
                }
            }

            var interactButton = _buttons.First(x => x.ButtonIndex == (int)TouchButtonIndex.Interactive);
            if(interactButton != null)
            {
                float angel = MathF.PI / 2 - MathF.PI * 3 / 64;
                interactButton.Position = new Vector2(
                _primaryButtonPosition.X - _buttonArrangementRadius * 1.45f * MathF.Cos(angel),
                _primaryButtonPosition.Y - _buttonArrangementRadius * 1.45f * MathF.Sin(angel));
            }

            var sitButton = _buttons.First(x => x.ButtonIndex == (int)TouchButtonIndex.Sit);
            if (sitButton != null)
            {
                float angel = MathF.PI * 3 / 64;
                sitButton.Position = new Vector2(
                _primaryButtonPosition.X - _buttonArrangementRadius * 1.45f * MathF.Cos(angel),
                _primaryButtonPosition.Y - _buttonArrangementRadius * 1.45f * MathF.Sin(angel));
            }
        }

        private void OnButtonClicked(object arg1, GuiItem.MouseLeftClickEvent @event)
        {
            var buttonIndex = (arg1 as TouchPadButton).ButtonIndex;
            HandleButtonPress(buttonIndex);
            GuiManager.IsTouchConsumed = true;
        }

        public void Update(GameTime gameTime, IList<TouchLocation> touchState)
        {
            if (!IsVisible) return;
            
            _presingButtonIndex = -1;

            foreach (var touch in touchState)
            {
                if (touch.State == TouchLocationState.Moved || touch.State == TouchLocationState.Pressed)
                {
                    //var touchPosition = touch.Position;
                    var touchPosition = ScreenToLogical(touch.Position); // 转换为逻辑坐标

                    // 检测是否在摇杆范围内
                    if (Vector2.Distance(touchPosition, JoystickCenter) < _joystickRadius)
                    {
                        JoystickThumbPosition = touchPosition;

                        // 限制摇杆范围
                        var direction = JoystickThumbPosition - JoystickCenter;
                        if (direction.Length() > _joystickRadius)
                        {
                            direction.Normalize();
                            JoystickThumbPosition = JoystickCenter + direction * _joystickRadius;
                        }
                    }

                    // 检测是否点击了按钮
                    //foreach (var button in _buttons)
                    //{
                    //    if (Vector2.Distance(touchPosition, button.Position) < button.ButtonRadius)
                    //    {
                    //        button.InvokeClick(button, null);
                    //        GuiManager.IsTouchConsumed = true;
                    //    }
                    //}

                    GuiManager.IsTouchConsumed = true;
                }
            }
            foreach (var button in _buttons)
            {
                button.Update(gameTime);
            }

            // 如果没有触摸，则复位摇杆位置
            if (touchState.Count == 0)
            {
                JoystickThumbPosition = JoystickCenter;
            }
        }

        public void Draw(SpriteBatch spriteBatch)
        {
            if (!IsVisible) return;

            // 绘制摇杆
            spriteBatch.Draw(_joystickBackground, JoystickCenter - new Vector2(_joystickRadius), null, Color.White, 0f, Vector2.Zero, 1f, SpriteEffects.None, 0f);
            spriteBatch.Draw(_joystickThumb, JoystickThumbPosition - new Vector2(_joystickThumb.Width / 2f, _joystickThumb.Height / 2f), null, Color.White, 0f, Vector2.Zero, 1f, SpriteEffects.None, 0f);

            // 绘制按钮
            //foreach (var position in _buttonPositions)
            //{
            //    spriteBatch.Draw(_buttonTexture, position - new Vector2(_buttonRadius), null, Color.White, 0f, Vector2.Zero, 1f, SpriteEffects.None, 0f);
            //}
            foreach (var button in _buttons)
            {
                //spriteBatch.Draw(button.ButtonTexture, button.Position - new Vector2(button.ButtonRadius), null, Color.White, 0f, Vector2.Zero, 1f, SpriteEffects.None, 0f);
                button.Draw(spriteBatch);
            }
        }

        private void HandleButtonPress(int buttonIndex)
        {
            // 模拟手柄按键
            switch (buttonIndex)
            {
                case 0: // A
                    Console.WriteLine("A button pressed");
                    break;
                case 1: // B
                    Console.WriteLine("B button pressed");
                    break;
                case 2: // X
                    Console.WriteLine("X button pressed");
                    break;
                case 3: // Y
                    Console.WriteLine("Y button pressed");
                    break;
            }

            // 模拟魔法使用
            var index = -1;
            if (buttonIndex > 0 && buttonIndex <= 4)
            {
                index = buttonIndex - 1;
            }

            if (index != -1)
            {
                var info = GuiManager.GetBottomMagicItemInfo(index);
                if (info != null && info.TheMagic != null)
                {
                    Globals.ThePlayer.CurrentMagicInUse = info;
                    Globals.ThePlayer.IsUseMagicByTouch = true;
                }
            }

            _presingButtonIndex = buttonIndex;
        }

        public bool IsPressingButton(int buttonIndex)
        {
            return buttonIndex == _presingButtonIndex;
        }

        //private Texture2D CreateCircleTexture(GraphicsDevice graphicsDevice, int radius, Color color)
        //{
        //    int diameter = radius * 2;
        //    var texture = new Texture2D(graphicsDevice, diameter, diameter);
        //    var data = new Color[diameter * diameter];

        //    for (int y = 0; y < diameter; y++)
        //    {
        //        for (int x = 0; x < diameter; x++)
        //        {
        //            var distance = Vector2.Distance(new Vector2(radius, radius), new Vector2(x, y));
        //            data[y * diameter + x] = distance <= radius ? color : Color.Transparent;
        //        }
        //    }

        //    texture.SetData(data);
        //    return texture;
        //}
        private Texture2D CreateCircleTexture(GraphicsDevice graphicsDevice, int radius, Color fillColor, Color borderColor, int borderWidth)
        {
            int diameter = radius * 2;
            var texture = new Texture2D(graphicsDevice, diameter, diameter);
            var data = new Color[diameter * diameter];

            for (int y = 0; y < diameter; y++)
            {
                for (int x = 0; x < diameter; x++)
                {
                    // 计算当前点与圆心的距离
                    var distance = Vector2.Distance(new Vector2(radius, radius), new Vector2(x, y));

                    if (distance <= radius) // 点在圆内
                    {
                        if (distance >= radius - borderWidth) // 在边框范围内
                        {
                            data[y * diameter + x] = borderColor; // 设置为边框颜色
                        }
                        else // 在内部区域
                        {
                            data[y * diameter + x] = fillColor; // 设置为填充颜色
                        }
                    }
                    else // 圆外的点
                    {
                        data[y * diameter + x] = Color.Transparent; // 设置为透明
                    }
                }
            }

            texture.SetData(data);
            return texture;
        }

        public void UpdateMagicItems()
        {
            for (int skillIndex = 3; skillIndex < 7; skillIndex++)
            {
                var index = GuiManager.BottomInterface.ToMagicListIndex(skillIndex);
                var magic = MagicListManager.Get(index);
                var icon = magic == null ? null : magic.Icon;
                _buttons[skillIndex - 2].BaseTexture = new Gui.Base.Texture(icon);
            }
        }

        public void OnPlayerFightStateChanged(Character character, bool isFighting)
        {
            if (isFighting)//|| !Globals.TheGame.IsSafe()
            {
                attackButton.BaseTexture = attackTexture;
            }
            else
            {
                attackButton.BaseTexture = talkTexture;
            }
        }
    }

    public enum TouchButtonIndex
    {
        None = -1,
        Attack = 0,
        Skill1 = 1,
        Skill2 = 2,
        Skill3 = 3,
        Skill4 = 4,
        Interactive = 5,
        Sit = 8,
        LittleMap = 9,
    }
}
