﻿using Engine.Gui.Base;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Texture = Engine.Gui.Base.Texture;

namespace Engine.Gui
{
    public class ColumnGui : GuiItem
    {
        private ColumnView _life;
        private ColumnView _thew;
        private ColumnView _mana;
        private ColumnView[] _items = new ColumnView[3];

        private int _leftAdjust;
        private int _topAdjust;

        public override Rectangle RegionInScreen //HACK一下
        {
            get
            {
                return new Rectangle((int)ScreenPosition.X,
                    (int)ScreenPosition.Y + Height / 2,
                    Width,
                    Height / 2);
            }
        }

        public ColumnGui()
        {
            var cfg = GuiManager.Setttings.Sections["BottomState"];
            BaseTexture = new Texture(Utils.GetAsf(null, cfg["Image"]));
            Width = BaseTexture.Width;
            Height = BaseTexture.Height;
            _leftAdjust = int.Parse(cfg["LeftAdjust"]);
            _topAdjust = int.Parse(cfg["TopAdjust"]);
            RePosition();
            InitializeItems();
        }

        public void RePosition()
        {
            Position = new Vector2((Globals.WindowWidth - Width) / 2f + _leftAdjust,
                Globals.WindowHeight - BaseTexture.Height + _topAdjust);
        }

        private void InitializeItems()
        {
            var cfg = GuiManager.Setttings.Sections["BottomState_Life"];
            _life = new ColumnView(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                new Texture(Utils.GetAsf(null, cfg["Image"])));
            cfg = GuiManager.Setttings.Sections["BottomState_Thew"];
            _thew = new ColumnView(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                new Texture(Utils.GetAsf(null, cfg["Image"])));
            cfg = GuiManager.Setttings.Sections["BottomState_Mana"];
            _mana = new ColumnView(this,
                new Vector2(int.Parse(cfg["Left"]), int.Parse(cfg["Top"])),
                int.Parse(cfg["Width"]),
                int.Parse(cfg["Height"]),
                new Texture(Utils.GetAsf(null, cfg["Image"])));
            _items[0] = _life;
            _items[1] = _thew;
            _items[2] = _mana;
        }

        private void Update(Player player)
        {
            if(player == null) return;
            
            // 限制更新频率，避免频繁更新导致的性能问题
            _life.Percent = MathHelper.Lerp(_life.Percent, player.Life/(float)player.LifeMax, 0.1f);
            _thew.Percent = MathHelper.Lerp(_thew.Percent, player.Thew/(float)player.ThewMax, 0.1f);
            _mana.Percent = MathHelper.Lerp(_mana.Percent, player.Mana/(float)player.ManaMax, 0.1f);
        }

        public override void Update(GameTime gameTime)
        {
            if(!IsShow) return;
            base.Update(gameTime);
            foreach (var item in _items)
            {
                item.Update(gameTime);
            }
            Update(Globals.ThePlayer);
        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            if(!IsShow) return;
            base.Draw(spriteBatch);
            foreach (var item in _items)
            {
                item.Draw(spriteBatch);
            }
            //Debug时查看碰撞区域
            //Texture2D text = TextureGenerator.GetColorTexture(Color.Red,Width,Height);
            //spriteBatch.Draw(text,RegionInScreen, Color.Red);
        }
    }
}