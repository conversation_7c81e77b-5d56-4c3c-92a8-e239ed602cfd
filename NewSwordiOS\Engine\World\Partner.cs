﻿using Engine.Gui;
using Engine.ListManager;
using IniParser;
using IniParser.Model;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Input;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace Engine
{
    public class Partner : Npc,IEquipable
    {
        public MagicListManager.MagicItemInfo CurrentMagicInUse { get; set; } //增加赋值
        public int PersonIndex { get; private set; }

        private Dictionary<int, Good> _equips = new Dictionary<int, Good>();
        private float _standingMilliseconds;
        private const float ListRestorePercent = 0.025f;
        private const float ThewRestorePercent = 0.04f;
        private const float ManaRestorePercent = 0.03f;

        public event Action<object, RelationArgs> RelationChanged;

        public Partner()
        {
            LevelIni = Globals.ThePlayer.LevelIni;
            LevelIniFile = Globals.ThePlayer.LevelIniFile;
            InitPartnerEquipment();
        }

        public Partner(Npc npc)
        {
            this.Name = npc.Name;

            this.Attack = npc.Attack;
            this.Attack2 = npc.Attack2;
            this.LifeMax = npc.LifeMax;
            this.Life = npc.Life;
            this.ThewMax = npc.ThewMax;
            this.Thew = npc.Thew;
            this.ManaMax = npc.ManaMax;
            this.Mana = npc.Mana;
            this.Attack = npc.Attack;
            this.Defend = npc.Defend;
            this.Defend2 = npc.Defend2;
            this.Evade = npc.Evade;
            this.LevelUpExp = npc.LevelUpExp;
            this.Exp = npc.Exp;
            this.Level = npc.Level;
            this.LevelIni = Globals.ThePlayer.LevelIni;
            this.LevelIniFile = Globals.ThePlayer.LevelIniFile;
            this.FlyIni = npc.FlyIni;
            this.BodyIni = npc.BodyIni;

            this.Kind = npc.Kind;
            this.Relation = npc.Relation;
            this.Sex = npc.Sex;

            this.NpcIni = npc.NpcIni;
            this.MapX = npc.MapX;
            this.MapY = npc.MapY;

            InitPartnerEquipment();
        }

        public Partner(string filePath)
            : base(filePath)
        {
            //var parser = new FileIniDataParser();
            //var data = parser.ReadFile(filePath, Globals.LocalEncoding);
            //var section = Utils.GetFirstSection(data);
            if (LevelIni != Globals.ThePlayer.LevelIni)
            {
                LevelIni = Globals.ThePlayer.LevelIni;
                LevelIniFile = Globals.ThePlayer.LevelIniFile;
            }
            InitPartnerEquipment();
        }

        private void InitPartnerEquipment()
        {
            try
            {
                PersonIndex = PartnerList.GetIndex(Name);
                if (PersonIndex > 0 && PersonIndex <= PartnerList.GetCount())
                {
                    foreach (var item in _equips)
                    {
                        Good equiptGood = item.Value;//GetEquiptGood(index - (GoodsListManager.PartnerEquipIndexEnd - 7 * Index));
                        if (equiptGood != null)
                        {
                            var partnerEquipInfo = new GoodsListManager.GoodsItemInfo(equiptGood, 1);
                            int index = GoodsListManager.PartnerEquipIndexEnd - 7 * PersonIndex + item.Key;
                            GoodsListManager.SetItemInfo(index, partnerEquipInfo);
                        }
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        public Partner(KeyDataCollection keyDataCollection)
            : base(keyDataCollection)
        {
            if (LevelIni != Globals.ThePlayer.LevelIni)
            {
                LevelIni = Globals.ThePlayer.LevelIni;
            }
            InitPartnerEquipment();
        }

        public override void Update(GameTime gameTime)
        {
            base.Update(gameTime);

            if (IsFighterFriend)
            {
                if (BodyFunctionWell)
                {
                    _standingMilliseconds += (float)gameTime.ElapsedGameTime.TotalMilliseconds;
                    if (_standingMilliseconds >= 1000)
                    {
                        Life += (int)(ListRestorePercent * LifeMax);
                        Thew += (int)(ThewRestorePercent * ThewMax);
                        Mana += (int)(ManaMax * ManaRestorePercent);
                        _standingMilliseconds = 0f;
                    }
                }
                else _standingMilliseconds = 0f;

                var keyboardState = Keyboard.GetState();
                if (Globals.TheGame.LastKeyboardState.IsKeyUp(Keys.B) && keyboardState.IsKeyDown(Keys.B))//
                {
                    //if (FollowTarget.IsEnemy)
                    //{
                    //    FollowTarget.CancleAttackTarget();
                    //}
                    CancleAttackTarget();
                    FollowTarget = null;
                    //RunTo(Globals.PlayerTilePosition);
                    Globals.ThePlayer.ResetPartnerPosition();
                } 
            }
        }

        public void AddExp(int amount, bool addMagicExp = false)
        {
            if (addMagicExp)
            {
                if (CurrentMagicInUse != null)
                {
                    AddMagicExp(CurrentMagicInUse, (int)(amount * Utils.UseMagicExpFraction));
                }
            }

            if (LevelUpExp <= 0) return;
            Exp += amount;
            if (Exp > LevelUpExp)
            {
                GuiManager.ShowMessage(Name + "的等级提升了");
                ToLevel(Exp);
            }
        }

        public void AddMagicExp(MagicListManager.MagicItemInfo info, int amount)
        {
            if (info == null ||
                info.TheMagic == null ||
                info.TheMagic.LevelupExp == 0 //Max level
                )
            {
                return;
            }
            info.Exp += amount;
            var levelupExp = info.TheMagic.LevelupExp;
            if (info.Exp >= levelupExp)
            {
                info.TheMagic = info.TheMagic.GetLevel(info.TheMagic.CurrentLevel + 1);
                if (info.TheMagic.LevelupExp == 0)
                {
                    //Magic is max level, make exp equal max exp
                    info.Exp = levelupExp;
                }
                GuiManager.ShowMessage("武功 " + info.TheMagic.Name + " 升级了");
            }
        }

        public bool UseDrug(Good drug)
        {
            if (drug != null && drug.Kind == Good.GoodKind.Drug)
            {
                LifeMax += drug.LifeMax;
                ThewMax += drug.ThewMax;
                ManaMax += drug.ManaMax;
                Life += drug.Life;
                Thew += drug.Thew;
                Mana += drug.Mana;
                switch (drug.TheEffectType)
                {
                    case Good.GoodEffectType.ClearFrozen:
                        ClearFrozen();
                        break;
                    case Good.GoodEffectType.ClearPoison:
                        ClearPoison();
                        break;
                    case Good.GoodEffectType.ClearPetrifaction:
                        ClearPetrifaction();
                        break;
                }
                return true;
            }
            return false;
        }

        private void ToLevel(int exp)
        {
            if (LevelIni != null)
            {
                var count = LevelIni.Count;
                var i = 1;
                for (; i <= count; i++)
                {
                    if (LevelIni.ContainsKey(i))
                    {
                        if (LevelIni[i].LevelUpExp > exp)
                            break;
                    }
                }
                SetLevelTo(i);
            }
        }

        public override void SetLevelTo(int level)
        {
            if (LevelIni == null)
            {
                Level = level;
                return;
            }
            Utils.LevelDetail detail = null;// currentDetail = null;
            if (LevelIni.ContainsKey(level))//当LevelIni改变时，此处匹配不正确，两个LevelIni
            {
                detail = LevelIni[level];
            }
            /*
            Utils.LevelDetail currentDetail = null;
            if (LevelIni.ContainsKey(Level))
            {
                currentDetail = LevelIni[Level];
            }*/
            if (detail != null)
            {
                /*
                LifeMax += (detail.LifeMax - currentDetail.LifeMax);
                ThewMax += (detail.ThewMax - currentDetail.ThewMax);
                ManaMax += (detail.ManaMax - currentDetail.ManaMax);
                Life = LifeMax;
                Thew = ThewMax;
                Mana = ManaMax;
                Attack += (detail.Attack - currentDetail.Attack);
                Attack2 += (detail.Attack2 - currentDetail.Attack2);
                Attack3 += (detail.Attack3 - currentDetail.Attack3);
                Defend += (detail.Defend - currentDetail.Defend);
                Defend2 += (detail.Defend2 - currentDetail.Defend2);
                Defend3 += (detail.Defend3 - currentDetail.Defend3);
                Evade += (detail.Evade - currentDetail.Evade);*/
                LifeMax = detail.LifeMax;
                ThewMax = detail.ThewMax;
                ManaMax = detail.ManaMax;
                Life = LifeMax;
                Thew = ThewMax;
                Mana = ManaMax;
                Attack = detail.Attack;
                Attack2 = detail.Attack2;
                Attack3 = detail.Attack3;
                Defend = detail.Defend;
                Defend2 = detail.Defend2;
                Defend3 = detail.Defend3;
                Evade = detail.Evade;
                LevelUpExp = detail.LevelUpExp;
                if (level > 1 && LevelIni.ContainsKey(level - 1) && Exp < LevelIni[level - 1].LevelUpExp)
                {
                    Exp = LevelIni[level - 1].LevelUpExp;
                }
                if (!string.IsNullOrEmpty(detail.NewMagic))
                {
                    AddMagic(detail.NewMagic);
                }
                RefreshEquipEffect();
            }
            else
            {
                Exp = 0;
                LevelUpExp = 0;
            }

            Level = level;
        }

        public void RefreshEquipEffect()
        {
            int start = GoodsListManager.PartnerEquipIndexEnd - 7 * PersonIndex + 1;
            GoodsListManager.RefreshPartnerEquipEffect(this, start);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="equip"></param>
        /// <param name="currentEquip"></param>
        /// <param name="justEffectType">Don't applay Attack, Defend,Evade,LifeMax,ThewMax,ManaMax, just equip effect</param>
        public void Equiping(Good equip, Good currentEquip, bool justEffectType = false)
        {
            //Save for restore
            var life = Life;
            var thew = Thew;
            var mana = Mana;

            UnEquiping(currentEquip, justEffectType);
            if (equip != null)
            {
                if (!justEffectType)
                {
                    Attack += equip.Attack;
                    Attack2 += equip.Attack2;
                    Attack3 += equip.Attack3;
                    Defend += equip.Defend;
                    Defend2 += equip.Defend2;
                    Defend3 += equip.Defend3;
                    Evade += equip.Evade;
                    LifeMax += equip.LifeMax;
                    ThewMax += equip.ThewMax;
                    ManaMax += equip.ManaMax;

                    if (!string.IsNullOrEmpty(equip.MagicIniWhenUse))
                    {
                        if (MagicListManager.IsMagicHided(equip.MagicIniWhenUse))
                        {
                            var info = MagicListManager.SetMagicHide(equip.MagicIniWhenUse, false);
                            if (info != null)
                            {
                                GuiManager.ShowMessage("武功" + info.TheMagic.Name + "已可使用");
                                GuiManager.UpdateMagicView();
                            }
                            else
                            {
                                GuiManager.ShowMessage("Good-MagicIniWhenUse错误");
                            }
                        }
                        else
                        {
                            AddMagic(equip.MagicIniWhenUse);
                        }
                    }
                }
                
            }

            //Restore
            Life = life;
            Thew = thew;
            Mana = mana;
        }

        public void UnEquiping(Good equip, bool justEffectType = false)
        {
            if (equip != null)
            {
                if (!justEffectType)
                {
                    Attack -= equip.Attack;
                    Attack2 -= equip.Attack2;
                    Attack3 -= equip.Attack3;
                    Defend -= equip.Defend;
                    Defend2 -= equip.Defend2;
                    Defend3 -= equip.Defend3;
                    Evade -= equip.Evade;
                    LifeMax -= equip.LifeMax;
                    ThewMax -= equip.ThewMax;
                    ManaMax -= equip.ManaMax;

                }             
            }
        }

        public Good GetEquiptGood(int positionIndex)
        {
            //int index = (int)position;
            Good equipGood = null;
            if (_equips.ContainsKey(positionIndex))
            {
                equipGood = _equips[positionIndex]; 
            }
            return equipGood;
        }

        protected override void AssignToValue(KeyData keyData)
        {
            try
            {
                switch (keyData.KeyName)
                {
                    case "Head":
                        if (keyData.Value != null && !string.IsNullOrEmpty(keyData.Value))
                        {
                            _equips.Add((int)Good.EquipPosition.Head, Utils.GetGood(keyData.Value)); 
                        }
                        return;
                    case "Neck":
                        if (keyData.Value != null && !string.IsNullOrEmpty(keyData.Value))
                        {
                            _equips.Add((int)Good.EquipPosition.Neck, Utils.GetGood(keyData.Value));
                        }
                        return;
                    case "Body":
                        if (keyData.Value != null && !string.IsNullOrEmpty(keyData.Value))
                        {
                            _equips.Add((int)Good.EquipPosition.Body, Utils.GetGood(keyData.Value));
                        }
                        return;
                    case "Back":
                        if (keyData.Value != null && !string.IsNullOrEmpty(keyData.Value))
                        {
                            _equips.Add((int)Good.EquipPosition.Back, Utils.GetGood(keyData.Value));
                        }
                        return;
                    case "Hand":
                        if (keyData.Value != null && !string.IsNullOrEmpty(keyData.Value))
                        {
                            _equips.Add((int)Good.EquipPosition.Hand, Utils.GetGood(keyData.Value));
                        }
                        return;
                    case "Wrist":
                        if (keyData.Value != null && !string.IsNullOrEmpty(keyData.Value))
                        {
                            _equips.Add((int)Good.EquipPosition.Wrist, Utils.GetGood(keyData.Value));
                        }
                        return;
                    case "Foot":
                        if (keyData.Value != null && !string.IsNullOrEmpty(keyData.Value))
                        {
                            _equips.Add((int)Good.EquipPosition.Foot, Utils.GetGood(keyData.Value));
                        }
                        return;
                        /*
                        case "IsRunDisabled":
                            IsRunDisabled = (keyData.Value != "0");
                            return;
                        case "IsJumpDisabled":
                            IsJumpDisabled = (keyData.Value != "0");
                            return;
                        case "IsFightDisabled":
                            IsFightDisabled = (keyData.Value != "0");
                            return;
                            */
                }
            }
            catch (Exception)
            {
                //do nothing
                //return;
            }
            base.AssignToValue(keyData);
        }

        public override void Save(KeyDataCollection keyDataCollection)
        {
            string[] positinName = { "Head", "Neck", "Body", "Back", "Hand", "Wrist", "Foot" };
            int startIndexOffset = GoodsListManager.PartnerEquipIndexEnd - 7 * PersonIndex;
            _equips.Clear();
            for (int i = 0; i < positinName.Length; i++)
            {
                int equIndex = startIndexOffset + i + 1;
                var equGood = GoodsListManager.Get(equIndex);
                if (equGood != null)
                {
                    AddKey(keyDataCollection, positinName[i], equGood.FileName);
                    _equips.Add(i + 1, equGood);
                }
            }
            base.Save(keyDataCollection);
            SavePersonDataToPlayerIni();
            if (_equips.Count > 0)
            {
                string partnerGoodsFilePath = Globals.GetRightpath(@"save\game\" + "goods" + PersonIndex + ".ini");
                GoodsListManager.SavePartnerEquipToFile(partnerGoodsFilePath, _equips);
            }
        }

        public void SavePersonDataToPlayerIni()
        {
            int startIndexOffset = GoodsListManager.PartnerEquipIndexEnd - 7 * PersonIndex;
            //对队友装备作的变更保存到对应的player文件当中去
            if (PersonIndex > 0 && PersonIndex <= PartnerList.GetCount() && PersonIndex != Globals.PlayerIndex)
            {
                string playerPath = Globals.GetRightpath(@"save\game\" + "Player" + PersonIndex + ".ini");
                if (File.Exists(Globals.GetRightpath(playerPath)))
                {
                    var parser = new FileIniDataParser();
                    var data = parser.ReadFile(Globals.GetRightpath(playerPath), Globals.LocalEncoding);
                    //data.Sections.AddSection("Init");
                    int txtLevel = 0;
                    if (data["Init"].ContainsKey("Level") && int.TryParse(data["Init"]["Level"], out txtLevel) && txtLevel <= Level)
                    {
                        SetKeyData(data["Init"], "Level", Level.ToString());
                        SetKeyData(data["Init"], "LevelIni", LevelIniFile);
                        SetKeyData(data["Init"], "Evade", Evade.ToString());
                        SetKeyData(data["Init"], "Attack", Attack.ToString());
                        SetKeyData(data["Init"], "Attack2", Attack2.ToString());
                        SetKeyData(data["Init"], "AttackLevel", AttackLevel.ToString());
                        SetKeyData(data["Init"], "Defend", Defend.ToString());
                        SetKeyData(data["Init"], "Defend2", Defend2.ToString());
                        SetKeyData(data["Init"], "Exp", Exp.ToString());
                        SetKeyData(data["Init"], "LevelUpExp", LevelUpExp.ToString());
                        SetKeyData(data["Init"], "Life", Life.ToString());
                        SetKeyData(data["Init"], "LifeMax", LifeMax.ToString());
                        SetKeyData(data["Init"], "Thew", Thew.ToString());
                        SetKeyData(data["Init"], "ThewMax", ThewMax.ToString());
                        SetKeyData(data["Init"], "Mana", Mana.ToString());
                        SetKeyData(data["Init"], "ManaMax", ManaMax.ToString());
                        string[] positinName = { "Head", "Neck", "Body", "Back", "Hand", "Wrist", "Foot" };
                        for (int i = 0; i < positinName.Length; i++)
                        {
                            int equIndex = startIndexOffset + i + 1;
                            var equGood = GoodsListManager.Get(equIndex);
                            if (equGood != null)
                            {
                                SetKeyData(data["Init"], positinName[i], equGood.FileName);
                            }
                        }
                        File.WriteAllText(Globals.GetRightpath(playerPath), data.ToString(), Globals.LocalEncoding);
                    } 
                }
            }
        }

        public override void SetRelation(int relation)
        {
            if (Relation != (int)RelationType.Friend && relation == (int)RelationType.Friend)
            {
                var args = new RelationArgs(this.Relation,relation);
                RelationChanged(this,args);
            }
            base.SetRelation(relation);
        }
    }

    public class RelationArgs : EventArgs
    {
        private int _orgin;

        public int Orgin
        {
            get { return _orgin; }
            set { _orgin = value; }
        }

        private int _target;

        public int Target
        {
            get { return _target; }
            set { _target = value; }
        }


        public RelationArgs(int orgin,int target)
        {
            _orgin = orgin;
            _target = target;
        }
    }
}
