using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using Foundation;

namespace NewSwordiOS
{
    public static class AssetsHelper
    {
        private static Dictionary<string, string> _fileMapping;
        private static readonly object _lock = new object();

        // 反向映射字典，优化查询性能
        private static Dictionary<string, string> _reverseFileMapping;

        public static void Initialize()
        {
            if (_fileMapping != null) return;

            lock (_lock)
            {
                if (_fileMapping != null) return;

                try
                {
                    // 尝试从应用包中读取映射文件
                    string mappingPath = NSBundle.MainBundle.PathForResource("assets_map", "json");
                    if (!string.IsNullOrEmpty(mappingPath))
                    {
                        try
                        {
                            string json = File.ReadAllText(mappingPath);
                            _fileMapping = JsonSerializer.Deserialize<Dictionary<string, string>>(json)
                                         ?? new Dictionary<string, string>();

                            // 创建反向映射字典
                            _reverseFileMapping = _fileMapping.ToDictionary(kvp => kvp.Value, kvp => kvp.Key);

                            Console.WriteLine($"Loaded asset mapping with {_fileMapping.Count} entries");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error parsing assets_map.json: {ex.Message}");
                            _fileMapping = new Dictionary<string, string>();
                            _reverseFileMapping = new Dictionary<string, string>();
                        }
                    }
                    else
                    {
                        // 如果找不到映射文件，创建空字典
                        _fileMapping = new Dictionary<string, string>();
                        _reverseFileMapping = new Dictionary<string, string>();
                    }
                }
                catch (Exception ex)
                {
                    // 记录错误日志，方便后续排查
                    Console.WriteLine($"Error loading assets map: {ex.Message}");
                    _fileMapping = new Dictionary<string, string>();
                    _reverseFileMapping = new Dictionary<string, string>();
                }
            }
        }

        public static string GetMappedPath(string originalPath)
        {
            if (_fileMapping == null)
            {
                throw new InvalidOperationException("AssetsHelper not initialized");
            }

            var normalizedPath = originalPath.Replace('/', '\\');

            // 优化：通过反向映射字典直接查找
            return _reverseFileMapping.TryGetValue(normalizedPath, out var mappedPath)
                   ? mappedPath
                   : normalizedPath;
        }

        public static string GetOriginalPath(string mappedPath)
        {
            if (_fileMapping == null)
            {
                throw new InvalidOperationException("AssetsHelper not initialized");
            }

            var normalizedPath = mappedPath.Replace('\\', '/');

            // 通过直接查找映射字典
            return _fileMapping.TryGetValue(normalizedPath, out var originalPath)
                   ? originalPath
                   : normalizedPath;
        }
    }
}
