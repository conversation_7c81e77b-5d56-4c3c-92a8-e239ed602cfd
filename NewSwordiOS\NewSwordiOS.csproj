﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net9.0-ios</TargetFrameworks>
    <OutputType>Exe</OutputType>
    <SupportedOSPlatformVersion>12.2</SupportedOSPlatformVersion>
    <CodesignKey>iPhone Developer</CodesignKey>
    <ProvisioningType>manual</ProvisioningType>
	  <ApplicationId>com.lightyearzh.TodoMan</ApplicationId>
    <!-- AOT Compilation enabled -->
    <RunAOTCompilation>false</RunAOTCompilation>
    <!--<EnableHotReload>false</EnableHotReload>-->
    <!--<UseInterpreter>true</UseInterpreter>-->
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <MtouchLink>None</MtouchLink>
    <MtouchDebug>True</MtouchDebug>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <MtouchLink>None</MtouchLink>
    <MtouchDebug>True</MtouchDebug>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="AssetsHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Content\effect\AlphaTest.xnb" />
    <None Remove="Content\effect\grayscale.xnb" />
    <None Remove="Content\effect\outedge.xnb" />
    <None Remove="Content\effect\refraction.xnb" />
    <None Remove="Content\effect\Transparent.xnb" />
    <None Remove="Content\effect\waterfall.xnb" />
    <None Remove="icon.png" />
    <None Remove="version.json" />
  </ItemGroup>
  <ItemGroup>
    <!--<EmbeddedResource Include="Content\effect\AlphaTest.xnb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Content\effect\grayscale.xnb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Content\effect\outedge.xnb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Content\effect\refraction.xnb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Content\effect\Transparent.xnb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Content\effect\waterfall.xnb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>-->
	  <BundleResource Include="asf\**\*.*" />
	  <BundleResource Include="ini\**\*.*" />
	  <BundleResource Include="map\**\*.*" />
	  <BundleResource Include="mpc\**\*.*" />
	  <BundleResource Include="save\**\*.*" />
	  <BundleResource Include="script\**\*.*" />
	  <BundleResource Include="Content\**\*.*" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="C5" Version="3.0.0" />
    <PackageReference Include="MonoGame.Framework.iOS" Version="3.8.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="TiledSharp" Version="1.0.1" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="version.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="INIFileParser">
      <HintPath>..\packages\INIFileParser.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="icon.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
  <Target Name="RestoreDotnetTools" BeforeTargets="Restore">
    <Message Text="Restoring dotnet tools" Importance="High" />
    <Exec Command="dotnet tool restore" />
  </Target>
</Project>