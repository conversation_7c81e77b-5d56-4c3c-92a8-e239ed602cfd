﻿using System;
using System.IO;
using Engine.Gui;
using Engine.Map;
using Engine.Script;
using Engine.Weather;
using IniParser.Model;
using Microsoft.Xna.Framework.Audio;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Media;
using Microsoft.Xna.Framework;

namespace Engine.Storage
{
    public static class Saver
    {
        private static bool isSaving = false;//多线程存储时避免资源锁
        private static void SaveGameFile()
        {
            var data = new IniData();

            //State
            data.Sections.AddSection("State");
            var state = data["State"];
            state.AddKey("Map", MapBase.MapFileName);
            state.AddKey("Npc", NpcManager.FileName);
            state.AddKey("Obj", ObjManager.FileName);
            state.AddKey("Bgm", BackgroundMusic.FileName);
            state.AddKey("Chr", Globals.PlayerIndex.ToString());
            state.AddKey("Time", string.Format(
                "{0:yyyy} 年{0:MM} 月{0:dd} 日 {0:HH} 时{0:mm} 分{0:ss} 秒",
                DateTime.Now));

            //Save npc obj
            NpcManager.SaveNpc();
            ObjManager.Save();

            //Option
			SaveOption();

            //Timer
            data.Sections.AddSection("Timer");
            var timer = data["Timer"];
            timer.AddKey("IsOn", GuiManager.IsTimerStarted() ? "1" : "0");
            if (GuiManager.IsTimerStarted())
            {
                timer.AddKey("TotalSecond", GuiManager.GetTimerCurrentSeconds().ToString());
                timer.AddKey("IsTimerWindowShow", GuiManager.IsTimerWindowHided() ? "0" : "1");
                timer.AddKey("IsScriptSet", ScriptExecuter.IsTimeScriptSet ? "1" : "0");
                timer.AddKey("TimerScript", ScriptExecuter.TimeScriptFileName);
                timer.AddKey("TriggerTime", ScriptExecuter.TimeScriptSeconds.ToString());
            }

            //Variables
			SaveVar();

            //ParallelScript
            data.Sections.AddSection("ParallelScript");
            ScriptManager.SaveParallelScript(data["ParallelScript"]);

            //Write to file
            string gameFilePath = Globals.GetRightpath(StorageBase.GameIniFilePath);
            try
            {
                // 确保目录结构存在
                string directoryPath = Path.GetDirectoryName(gameFilePath);
                if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                    Log.LogMessage("Created directory: " + directoryPath);
                }

                File.WriteAllText(gameFilePath, data.ToString(), Globals.LocalEncoding);
            }
            catch (Exception ex)
            {
                // 记录错误信息以便调试
                Log.LogMessage("SaveGameFile Error: " + ex.Message);
                Log.LogMessage("Path: " + gameFilePath);
                Log.LogMessage("StackTrace: " + ex.StackTrace);
            }
        }
        //新剑添加开始
        public static void SaveVar()
        {
            //Variables
            var data = new IniData();
            data.Sections.AddSection("Var");
            ScriptExecuter.SaveVariables(data["Var"]);

            string varFilePath = Globals.GetRightpath(StorageBase.VarIniFilePath);
            try
            {
                // 确保目录结构存在
                string directoryPath = Path.GetDirectoryName(varFilePath);
                if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                    Log.LogMessage("Created directory: " + directoryPath);
                }

                File.WriteAllText(varFilePath, data.ToString(), Globals.LocalEncoding);
                Log.LogMessage("SaveVar:" + Globals.LocalEncoding.EncodingName);
            }
            catch (Exception ex)
            {
                // 记录错误信息以便调试
                Log.LogMessage("SaveVar Error: " + ex.Message);
                Log.LogMessage("Path: " + varFilePath);
                Log.LogMessage("StackTrace: " + ex.StackTrace);
            }
        }
        public static void SaveOption()
        {
            //Option
            var data = new IniData();
            data.Sections.AddSection("Option");
            var option = data["Option"];
            option.AddKey("MapTime", MapBase.MapTime.ToString());
            option.AddKey("SnowShow", (WeatherManager.IsSnowing ? 1 : 0).ToString());
            option.AddKey("RainFile", WeatherManager.IsRaining ? WeatherManager.RainFileName : "");
            option.AddKey("Water", Globals.IsWaterEffectEnabled ? "1" : "0");
            option.AddKey("MpcStyle", StorageBase.GetStringFromColor(MapBase.DrawColor));
            option.AddKey("AsfStyle", StorageBase.GetStringFromColor(Sprite.DrawColor));
            if (Globals.IsSaveDisabled)
            {
                option.AddKey("SaveDisabled", "1");
            }
            if (Globals.IsDropGoodWhenDefeatEnemyDisabled)
            {
                option.AddKey("IsDropGoodWhenDefeatEnemyDisabled", "1");
            }
            //Save Option
            //确保目录存在并创建文件
            string optionFilePath = Globals.GetRightpath(StorageBase.OptionIniFilePath);
            try
            {
                // 确保目录结构存在
                string directoryPath = Path.GetDirectoryName(optionFilePath);
                if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                    Log.LogMessage("Created directory: " + directoryPath);
                }

                // 文件不存在时，先创建
                if (!File.Exists(optionFilePath))
                {
                    File.Create(optionFilePath).Close();
                }
                File.WriteAllText(optionFilePath, data.ToString(), Globals.LocalEncoding);
                Log.LogMessage("SaveOption:" + Globals.LocalEncoding.EncodingName);
            }
            catch (Exception ex)
            {
                // 记录错误信息以便调试
                Log.LogMessage("SaveOption Error: " + ex.Message);
                Log.LogMessage("Path: " + optionFilePath);
                Log.LogMessage("StackTrace: " + ex.StackTrace);
            }
        }
        //新剑添加结束

        public static void SavePlayer()
        {
            var data = new IniData();
            data.Sections.AddSection("Init");
            Globals.ThePlayer.Save(data["Init"]);

            string playerFilePath = Globals.GetRightpath(StorageBase.PlayerFilePath);
            try
            {
                // 确保目录结构存在
                string directoryPath = Path.GetDirectoryName(playerFilePath);
                if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                    Log.LogMessage("Created directory: " + directoryPath);
                }

                File.WriteAllText(playerFilePath, data.ToString(), Globals.LocalEncoding);
            }
            catch (Exception ex)
            {
                // 记录错误信息以便调试
                Log.LogMessage("SavePlayer Error: " + ex.Message);
                Log.LogMessage("Path: " + playerFilePath);
                Log.LogMessage("StackTrace: " + ex.StackTrace);
            }
        }

        private static void SavePartner()
        {
            NpcManager.SavePartner(StorageBase.PartnerFileName);
        }

        public static void SaveMagicGoodMemoList()
        {
            GuiManager.Save(StorageBase.MagicListFilePath,
                StorageBase.GoodsListFilePath,
                StorageBase.MemoListIniFilePath);
        }

        private static void SaveTraps()
        {
            MapBase.SaveTrap(StorageBase.TrapsFilePath);
        }

        private static void SaveTrapIgnoreList()
        {
            MapBase.SaveTrapIndexIgnoreList(StorageBase.TrapIndexIgnoreListFilePath);
        }

        /// <summary>
        /// Save game to "save/game" directory.
        /// </summary>
        public static void SaveGame()
        {
            SaveGameFile();
            SaveMagicGoodMemoList();
            SavePlayer();
            SavePartner();
            SaveTraps();
            SaveTrapIgnoreList();
        }

        /// <summary>
        /// Save game to 0-7
        /// </summary>
        /// <param name="index">Save index</param>
        /// <param name="snapShot">Game snapshot</param>
        public static void SaveGame(int index, Texture2D snapShot)
        {
            if (!StorageBase.IsIndexInRange(index) ||
                GameState.State != GameState.StateType.Playing)
            {
                return;
            }
            if (!isSaving)
            {
                isSaving = true;
                StorageBase.SaveSaveSnapShot(index, snapShot);
                SaveGame();
                StorageBase.CopyGameToSave(index);
                isSaving = false;
            }
        }

        /// <summary>
        /// 增加快速存档，不截图
        /// </summary>
        public static void QuickSave()
        {
            var index = 10;
            //Texture2D snapshot = TextureGenerator.GetColorTexture(Color.White,50,50);
            SaveGame(index, null);
        }
    }
}