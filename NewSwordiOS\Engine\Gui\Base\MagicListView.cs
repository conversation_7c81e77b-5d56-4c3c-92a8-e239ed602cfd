﻿using System;
using IniParser.Model;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input;

namespace Engine.Gui.Base
{
    public class MagicListView : GuiItem,IGamepadActive
    {
        private ScrollBar _scrollBar;
        private DragDropItem[] _items = new DragDropItem[12];
        private KeyDataCollection _config;
        public event Action<object, ListScrollEvent> Scrolled;

        public int CurrentItemIndex { get; private set; } = -1;

        public int CurrentScrollValue
        {
            get
            {
                if (_scrollBar == null)
                    return 0;
                return (int)_scrollBar.Value;
            }
        }

        public bool Actived { get ; set; }

        public MagicListView(GuiItem parent,
            Vector2 position,
            Vector2 scrollBarPosition,
            int width,
            int height,
            Texture baseTexture,
            int rowCouunts,
            KeyDataCollection config,
            int slideBarWidth,
            int slideBarHeight,
            string slideButtonImage,
            bool useTopLeftText = true)
            : base(parent, position, width, height, baseTexture)
        {
            _config = config;
            InitializeItems(useTopLeftText);
            var slideTexture = Utils.GetAsf(null, slideButtonImage);
            var slideBaseTexture = new Texture(slideTexture);
            var slideClikedTexture = new Texture(slideTexture, 0, 1);
            var slideButton = new GuiItem(this,
                Vector2.Zero,
                slideBaseTexture.Width,
                slideBaseTexture.Height,
                slideBaseTexture,
                null,
                slideClikedTexture,
                null,
                Utils.GetSoundEffect("界-大按钮.wav"));
            _scrollBar = new ScrollBar(this,
                slideBarWidth,
                slideBarHeight,
                null,
                ScrollBar.ScrollBarType.Vertical,
                slideButton,
                scrollBarPosition,
                0,
                rowCouunts - 6,
                0);
            _scrollBar.Scrolled += delegate(object arg1, ScrollBar.ScrolledEvent arg2)
            {
                if (Scrolled != null)
                    Scrolled(this, new ListScrollEvent(arg2.Value));
            };

            MouseScrollUp += (arg1, arg2) => _scrollBar.Value -= 1;
            MouseScrollDown += (arg1, arg2) => _scrollBar.Value += 1;
        }

        private void InitializeItems(bool useTopLeftText)
        {
            var c = _config;
            _items[0] = new DragDropItem(this, new Vector2(int.Parse(c["Item_Left_1"]), int.Parse(c["Item_Top_1"])), int.Parse(c["Item_Width_1"]), int.Parse(c["Item_Height_1"]), null, null, useTopLeftText);
            _items[1] = new DragDropItem(this, new Vector2(int.Parse(c["Item_Left_2"]), int.Parse(c["Item_Top_2"])), int.Parse(c["Item_Width_2"]), int.Parse(c["Item_Height_2"]), null, null, useTopLeftText);
            _items[2] = new DragDropItem(this, new Vector2(int.Parse(c["Item_Left_3"]), int.Parse(c["Item_Top_3"])), int.Parse(c["Item_Width_3"]), int.Parse(c["Item_Height_3"]), null, null, useTopLeftText);
            _items[3] = new DragDropItem(this, new Vector2(int.Parse(c["Item_Left_4"]), int.Parse(c["Item_Top_4"])), int.Parse(c["Item_Width_4"]), int.Parse(c["Item_Height_4"]), null, null, useTopLeftText);
            _items[4] = new DragDropItem(this, new Vector2(int.Parse(c["Item_Left_5"]), int.Parse(c["Item_Top_5"])), int.Parse(c["Item_Width_5"]), int.Parse(c["Item_Height_5"]), null, null, useTopLeftText);
            _items[5] = new DragDropItem(this, new Vector2(int.Parse(c["Item_Left_6"]), int.Parse(c["Item_Top_6"])), int.Parse(c["Item_Width_6"]), int.Parse(c["Item_Height_6"]), null, null, useTopLeftText);
            _items[6] = new DragDropItem(this, new Vector2(int.Parse(c["Item_Left_7"]), int.Parse(c["Item_Top_7"])), int.Parse(c["Item_Width_7"]), int.Parse(c["Item_Height_7"]), null, null, useTopLeftText);
            _items[7] = new DragDropItem(this, new Vector2(int.Parse(c["Item_Left_8"]), int.Parse(c["Item_Top_8"])), int.Parse(c["Item_Width_8"]), int.Parse(c["Item_Height_8"]), null, null, useTopLeftText);
            _items[8] = new DragDropItem(this, new Vector2(int.Parse(c["Item_Left_9"]), int.Parse(c["Item_Top_9"])), int.Parse(c["Item_Width_9"]), int.Parse(c["Item_Height_9"]), null, null, useTopLeftText);
            _items[9] = new DragDropItem(this, new Vector2(int.Parse(c["Item_Left_10"]), int.Parse(c["Item_Top_10"])), int.Parse(c["Item_Width_10"]), int.Parse(c["Item_Height_10"]), null, null, useTopLeftText);
            _items[10] = new DragDropItem(this, new Vector2(int.Parse(c["Item_Left_11"]), int.Parse(c["Item_Top_11"])), int.Parse(c["Item_Width_11"]), int.Parse(c["Item_Height_11"]), null, null, useTopLeftText);
            _items[11] = new DragDropItem(this, new Vector2(int.Parse(c["Item_Left_12"]), int.Parse(c["Item_Top_12"])), int.Parse(c["Item_Width_12"]), int.Parse(c["Item_Height_12"]), null, null, useTopLeftText);
        }

        public void RegisterItemDragHandler(Action<object, DragDropItem.DragEvent> handler)
        {
            foreach (var dragDropItem in _items)
            {
                dragDropItem.Drag += handler;
            }
        }

        public void RegisterItemDropHandler(Action<object, DragDropItem.DropEvent> handler)
        {
            foreach (var dragDropItem in _items)
            {
                dragDropItem.Drop += handler;
            }
        }

        public void RegisterItemMouseRightClickeHandler(Action<object, MouseRightClickEvent> handler)
        {
            foreach (var dragDropItem in _items)
            {
                dragDropItem.RightClick += handler;
            }
        }

        public void RegisterItemMouseLeftClickeHandler(Action<object, MouseLeftClickEvent> handler)
        {
            foreach(var dragDropItem in _items)
            {
                dragDropItem.Click += handler;
            }
        }

        public void RegisterItemMouseStayOverHandler(Action<object, MouseEvent> handler)
        {
            foreach (var dragDropItem in _items)
            {
                dragDropItem.MouseStayOver += handler;
            }
        }

        public void RegisterItemMouseLeaveHandler(Action<object, MouseEvent> handler)
        {
            foreach (var dragDropItem in _items)
            {
                dragDropItem.MouseLeave += handler;
            }
        }

        /// <summary>
        /// List index begin at 1
        /// </summary>
        /// <param name="itemIndex">range: 0 - 11</param>
        /// <returns></returns>
        public int ToListIndex(int itemIndex)
        {
            return CurrentScrollValue * 2 + itemIndex + 1;
        }

        /// <summary>
        /// List index begin at 1
        /// </summary>
        /// <param name="listIndex"></param>
        /// <param name="index">range: 0 - 11</param>
        /// <returns></returns>
        public bool IsItemShow(int listIndex, out int index)
        {
            var start = ToListIndex(0);
            var end = ToListIndex(11);
            if (listIndex >= start && listIndex <= end)
            {
                index = listIndex - start;
                return true;
            }
            index = 0;
            return false;
        }

        public void SetListItem(int index, Texture texture, object data)
        {
            if (index >= 0 && index < 12)
            {
                _items[index].BaseTexture = texture;
                _items[index].Data = data;
            }
        }

        public void SetListItemTexture(int index, Texture texture)
        {
            if (index >= 0 && index < 12)
            {
                _items[index].BaseTexture = texture;
            }
        }

        public void SetItemTopLeftText(int index, string text)
        {
            if (index >= 0 && index < 12)
            {
                _items[index].TopLeftText = text;
            }
        }

        public void SetMaxRow(int value)
        {
            _scrollBar.MaxValue = value;
        }

        public void ScrollToRow(int value)
        {
            _scrollBar.Value = value;
        }

        public override void Update(GameTime gameTime)
        {
            if (!IsShow) return;

            base.Update(gameTime);
            _scrollBar.Update(gameTime);
            //foreach (var dragDropItem in _items)
            //{
            //    dragDropItem.Update(gameTime);
            //}
            var leftClick = false;
            if (Actived)
            {
                var keyboard = Keyboard.GetState();
                var gamePad = GamePad.GetState(PlayerIndex.One);
                HandleInput(keyboard, gamePad);
                leftClick = GuiManager.CheckClick(keyboard, gamePad); 
            }
            for (int i = 0; i < _items.Length; i++)
            {
                DragDropItem dragDropItem = _items[i];
                if (Actived && CurrentItemIndex >= 0 && i == CurrentItemIndex)
                {
                    dragDropItem.IsFocus = true;
                    if (leftClick)
                    {
                        dragDropItem.InvokeClick(dragDropItem, null);
                    }
                }
                else
                {
                    dragDropItem.IsFocus = false;
                }
                dragDropItem.Update(gameTime);
            }
        }


        private void HandleInput(KeyboardState keyboard, GamePadState gamePad)
        {
            bool hasInput = false;

            //手柄、键盘的上下左右可以选择Item，通过改变CurrentItemIndex来实现。
            //上-往上移动一行，下-往下移动一行，左-往左移动一格，右-往右移动一格。每行2格,显示区域为6行
            if (keyboard.IsKeyDown(Keys.Left) ||
                (gamePad.IsButtonDown(Buttons.DPadLeft) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.DPadLeft)))
            {
                CurrentItemIndex -= 1;
                hasInput = true;
            }
            else if (keyboard.IsKeyDown(Keys.Right) ||
                (gamePad.IsButtonDown(Buttons.DPadRight) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.DPadRight)))
            {
                CurrentItemIndex += 1;
                hasInput = true;
            }
            else if (keyboard.IsKeyDown(Keys.Up) ||
                (gamePad.IsButtonDown(Buttons.DPadUp) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.DPadUp)))
            {
                CurrentItemIndex -= 2;
                hasInput = true;
            }
            else if (keyboard.IsKeyDown(Keys.Down) ||
                (gamePad.IsButtonDown(Buttons.DPadDown) && Globals.TheGame.LastGamepadState.IsButtonUp(Buttons.DPadDown)))
            {
                CurrentItemIndex += 2;
                hasInput = true;
            }
            //界限判断，<0往前滚动，>11往后滚动
            if (hasInput && CurrentItemIndex < 0)
            {
                if (CurrentScrollValue > 0)
                {
                    _scrollBar.Value -= 1;
                }
                CurrentItemIndex = CurrentItemIndex + 2;
            }
            else if (hasInput && CurrentItemIndex > 11)
            {
                if (CurrentScrollValue < _scrollBar.MaxValue - 1)
                {
                    _scrollBar.Value += 1;
                }
                CurrentItemIndex = CurrentItemIndex - 2;
            }
            if (hasInput)
            {
                CurrentItemIndex = CurrentItemIndex % 12;
            }
        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;

            base.Draw(spriteBatch);
            foreach (var dragDropItem in _items)
            {
                dragDropItem.Draw(spriteBatch);
            }
            _scrollBar.Draw(spriteBatch);
        }

        #region Event
        public abstract class ListEvent : EventArgs
        {
            public int ScrollValue { private set; get; }

            public ListEvent(int scrollValue)
            {
                ScrollValue = scrollValue;
            }
        }

        public class ListScrollEvent : ListEvent
        {
            public ListScrollEvent(int scrollValue)
                : base(scrollValue)
            { }
        }
        #endregion Event
    }
}