﻿using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Audio;
using Microsoft.Xna.Framework.Graphics;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Engine.Gui.Base
{
    public class CheckItem : GuiItem
    {
		private bool _isChecked = false	;

		public bool IsChecked
		{
			get { return _isChecked; }
			set { 
                _isChecked = value;
                CheckedChanged?.Invoke(this, EventArgs.Empty);
            }
		}

        public event Action<object, EventArgs> CheckedChanged;

        public Texture CheckedTexture { get; set; }
        public Texture UncheckedTexture { get; set; }

        public CheckItem() : base() { }

        public CheckItem(
                GuiItem parent,
                Vector2 position,
                int width,
                int height,
                Texture baseTexture,
                Texture mouseOverTexture = null,
                Texture clickedTexture = null,
                SoundEffect enteredSound = null,
                SoundEffect clickedSound = null) :
            base(parent,
                position,
                width,
                height,
                baseTexture,
                mouseOverTexture,
                clickedTexture,
                enteredSound,
                clickedSound)
        {
        }

        public CheckItem(
        GuiItem parent,
        Vector2 position,
        int width,
        int height,
        Texture uncheckedTexture,
        Texture checkedTexture,
        Texture mouseOverTexture = null,
        Texture clickedTexture = null,
        SoundEffect enteredSound = null,
        SoundEffect clickedSound = null) : this(parent,
        position,
        width,
        height,
        uncheckedTexture,
        mouseOverTexture,
        clickedTexture,
        enteredSound,
        clickedSound)
        {
            CheckedTexture = checkedTexture;
            UncheckedTexture = uncheckedTexture;
            BaseTexture = uncheckedTexture;
        }

        //public override void Update(GameTime gameTime)
        //{
        //    base.Update(gameTime);
        //}

        public override void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;

            if(IsChecked)
            {
                CheckedTexture.Draw(spriteBatch, ScreenPosition);
            }
            else
            {
                UncheckedTexture.Draw(spriteBatch, ScreenPosition);
            }
        }
    }
}
