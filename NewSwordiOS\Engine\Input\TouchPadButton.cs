﻿using Engine.Gui;
using Engine.Gui.Base;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input.Touch;
using Microsoft.Xna.Framework.Input;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Engine
{
    public class TouchPadButton : GuiItem
    {
        private float _buttonRadius;
        public float ButtonRadius { get => _buttonRadius; set => _buttonRadius = value; }
        public Texture2D ButtonTexture { get => _buttonTexture; set => _buttonTexture = value; }
        public int ButtonIndex { get; internal set; }

        private Texture2D _buttonTexture;

        public override void Update(GameTime gameTime)
        {
            //base.Update(gameTime);
            //用圆形检测是否触摸到了按钮
            Vector2 screenPosition = Vector2.Zero;
            bool isTouching = false;
            TouchLocation? touch = null;
            // 遍历当前帧的触摸状态
            foreach (var touchLocation in GuiManager.CurrentTouchState)
            {
                var logicPosition = new Vector2(touchLocation.Position.X / Globals.Scale, touchLocation.Position.Y / Globals.Scale);
                // 判断触摸点是否在 GUIItem 的范围内
                if (Vector2.Distance(logicPosition, this.Position) < this.ButtonRadius)
                {
                    screenPosition = logicPosition;
                    touch = touchLocation;
                    isTouching = true;
                    break; // 如果找到了一个触摸点，不再继续检查其他触摸点
                }
            }
            if (isTouching && touch.HasValue)
            {
                var touchLocation = touch.Value;
                if (touchLocation.State == TouchLocationState.Pressed)
                {
                    IsTapClicked = true;
                    if (ClickedTexture != null) ClickedTexture.CurrentFrameIndex = 0;

                    if (ClickedSound != null) ClickedSound.Play();
                }

                // 处理触摸释放事件
                if (touchLocation.State == TouchLocationState.Released && IsTapClicked)
                {
                    InvokeClick(this, new MouseLeftClickEvent(Vector2.Zero,screenPosition));
                    IsTapClicked = false;
                }
                //如果处理过
                GuiManager.IsTouchConsumed = true;
            }
        }

        public override void Draw(SpriteBatch spriteBatch)
        {
            if (!IsShow) return;
            spriteBatch.Draw(_buttonTexture, Position - new Vector2(_buttonRadius), null, Color.White, 0f, Vector2.Zero, 1f, SpriteEffects.None, 0f);
            //base.Draw(spriteBatch);
            if (BaseTexture != null)
            {
                BaseTexture.Draw(spriteBatch, ScreenPosition - new Vector2(BaseTexture.Width / 2f, BaseTexture.Height / 2f));
            }
        }
    }
}
